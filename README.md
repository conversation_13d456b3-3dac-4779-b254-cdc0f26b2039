# NoMoreLonelyLife

A Next.js template for building Farcaster mini-apps with ChatGPT-like interface, integrated with nmllagent backend.

## ✨ **Architecture Highlights**

- **Engineering Minimization**: Clean, simple codebase following minimal engineering principles
- **Official SDK Only**: Uses `@farcaster/miniapp-sdk` exclusively, no deprecated dependencies
- **Clear Separation**: Frontend handles UI, backend handles AI and notifications
- **Zero Complexity**: No custom fallback modes or complex storage logic
- **Secure Access**: Lock screen with password protection and API-ready invitation code system

## 🚀 Quick Start

### Development

```bash
# Install dependencies
pnpm install

# Start development server
pnpm run dev
```

The app will be available at `http://localhost:3001`

### Production Build

```bash
# Build for production
pnpm run build

# Start production server
pnpm run start
```

## 🌐 Deployment

### Vercel Deployment

1. **Fork/Clone this repository**

2. **Set up environment variables in Vercel:**
   - Go to your Vercel project settings
   - Navigate to "Environment Variables"
   - Add the following variables:

   | Variable | Value | Description |
   |----------|-------|-------------|
   | `NEXT_PUBLIC_URL` | `https://your-app.vercel.app` | Your app's public URL |
   | `NEXT_PUBLIC_NMLLAGENT_URL` | `https://your-backend.com` | Your nmllagent backend URL |
   | `NMLLAGENT_API_TOKEN` | `your-secure-token` | API token for backend communication |

3. **Deploy:**
   - Connect your repository to Vercel
   - Deploy automatically on push to main branch

### Environment Variables

#### Frontend Configuration (Required)
- `NEXT_PUBLIC_URL`: The public URL of your deployed app
- `NEXT_PUBLIC_NMLLAGENT_URL`: URL of your nmllagent backend service
- `NMLLAGENT_API_TOKEN`: Secure token for backend communication

#### Backend Configuration (nmllagent/.env)
Frame notifications are handled entirely by the backend. Configure these in your nmllagent instance:
- `NEYNAR_API_KEY`: Neynar API key for notifications
- `NEYNAR_CLIENT_ID`: Neynar client ID for notifications
- `FARCASTER_MINIAPP_URL`: Your frontend URL (for notification target)

### Demo Mode

If `NEXT_PUBLIC_NMLLAGENT_URL` is not configured or points to localhost, the app will run in demo mode:
- Mock AI responses will be returned
- Chat history will be empty
- All UI features will work for testing
- Frame notifications will not work (requires backend configuration)

## 🔧 Development

### Project Structure

```
├── app/                    # Next.js app directory
│   ├── api/               # API routes (proxy to nmllagent)
│   ├── .well-known/       # Farcaster configuration
│   └── page.tsx           # Main page
├── components/            # React components
│   ├── features/         # Feature components
│   │   ├── chat/         # Chat interface components
│   │   └── notifications/ # Notification setup (simplified)
│   ├── providers/        # React context providers
│   └── ui/               # UI components
├── lib/                  # Utilities
│   ├── constants.ts      # App constants
│   └── notifications.ts  # Notification helpers (simplified)
└── public/               # Static assets
    └── images/           # Farcaster required images
```

### Key Features

- **🎯 Engineering Minimization**: Clean, maintainable codebase with minimal complexity
- **📱 Farcaster SDK Integration**: Uses official `@farcaster/miniapp-sdk` exclusively
- **💬 ChatGPT-like Interface**: Modern chat interface with streaming responses
- **🔗 Backend Integration**: Connects to nmllagent backend for AI responses
- **🔔 Frame Notifications**: Simple notification setup (handled by backend)
- **🔒 Lock Screen**: Password-protected access with API-ready invitation system
- **📱 Mobile Responsive**: Optimized for mobile devices
- **🔧 TypeScript**: Full TypeScript support
- **🚫 Zero Dependencies**: No deprecated or unnecessary packages

### API Endpoints

- `/api/nmllagent/message` - Send messages to AI backend
- `/api/nmllagent/history` - Get chat history
- `/api/nmllagent/test` - Test backend connectivity
- `/api/verify-unlock` - Lock screen password verification
- `/.well-known/farcaster.json` - Farcaster app configuration

## 🏗️ Architecture

### Simplified Design Principles

This template follows **engineering minimization** principles:

1. **Single Responsibility**: Frontend handles UI, backend handles AI and notifications
2. **Official SDKs Only**: Uses `@farcaster/miniapp-sdk`, no deprecated packages
3. **Zero Custom Fallbacks**: No complex backup modes or custom protocols
4. **Clear Separation**: No mixed concerns between frontend and backend

### Notification System

```
User adds MiniApp → Neynar stores tokens → Backend sends notifications
```

- **Frontend**: Calls `actions.addMiniApp()`, shows success/failure
- **Backend**: Uses Neynar SDK to send notifications
- **No Storage**: Frontend doesn't store tokens (Neynar handles everything)

### Dependencies

**Core Dependencies** (minimal set):
- `@farcaster/miniapp-sdk` - Official Farcaster SDK
- `@farcaster/miniapp-node` - Server-side utilities
- `@farcaster/miniapp-wagmi-connector` - Wallet integration
- `next` - React framework

**Removed Dependencies** (for simplicity):
- ❌ `@neynar/react` - Deprecated, replaced by official SDK
- ❌ `@farcaster/frame-sdk` - Deprecated, replaced by miniapp-sdk
- ❌ Custom token management - Handled by Neynar SDK

## 🐛 Troubleshooting

### Common Issues

1. **"Application Error" on Vercel**
   - Check that `NEXT_PUBLIC_URL` is set correctly
   - Verify all environment variables are configured
   - Check Vercel function logs for detailed errors

2. **Blank page or client-side errors**
   - Open browser developer tools to see JavaScript errors
   - Check Vercel function logs for detailed errors
   - Ensure all required images are present in `/public/images/`

3. **Backend connection issues**
   - Verify `NEXT_PUBLIC_NMLLAGENT_URL` is accessible from the internet
   - Test backend connectivity with `/api/nmllagent/test`
   - Check CORS settings on your backend

### Debug Endpoints

- `/api/nmllagent/test` - Tests backend connectivity

### Logs

Check Vercel function logs or browser console for detailed error information.

## 📱 Farcaster Integration

This template includes:
- **Official SDK**: Uses `@farcaster/miniapp-sdk` exclusively
- **User Authentication**: Farcaster user profile access
- **Frame Notifications**: Simple notification setup (backend-handled)
- **Mobile Optimized**: Responsive design for mobile devices
- **Proper Configuration**: Valid farcaster.json manifest

Required images (already included):
- `/public/images/icon.png` - App icon (192x192)
- `/public/images/feed.png` - Feed image (1200x630)
- `/public/images/splash.png` - Splash screen (1200x630)

## 📚 Documentation

- **[Setup Guide](./SETUP_GUIDE.md)** - Quick start and deployment
- **[Lock Screen Guide](./LOCK_SCREEN.md)** - Lock screen implementation and usage
- **[Architecture Optimization](./ARCHITECTURE_OPTIMIZATION.md)** - Technical details
- **[Token Mode Cleanup](./TOKEN_MODE_CLEANUP.md)** - Simplification process

## 🔗 Related Projects

- [nmllagent](../nmllagent) - ElizaOS-based backend agent
- [ElizaOS](https://github.com/elizaos/eliza) - AI agent framework

## 🎯 Philosophy

This template follows **engineering minimization** principles:
- Use official SDKs only
- Avoid over-engineering
- Keep dependencies minimal
- Prioritize maintainability over features
- Clear separation of concerns

## 📄 License

MIT License - see LICENSE file for details.
