{"name": "monad-farcaster-miniapp-template", "license": "MIT", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "test:backend": "node scripts/check-backend-integration.js"}, "dependencies": {"@farcaster/auth-client": "^0.7.0", "@farcaster/miniapp-node": "0.1.5", "@farcaster/miniapp-sdk": "0.1.6", "@farcaster/miniapp-wagmi-connector": "1.0.0", "@kuru-labs/kuru-sdk": "^0.0.61", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.64.2", "@types/jsonwebtoken": "^9.0.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "15.3.4", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "viem": "^2.31.7", "wagmi": "^2.15.6", "zod": "^3.25.67"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@types/node": "^24.0.10", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}, "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184", "pnpm": {"ignoredBuiltDependencies": ["@biomejs/biome"], "onlyBuiltDependencies": ["@biomejs/biome"]}}