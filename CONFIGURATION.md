# Configuration Guide

This guide explains all the environment variables and configuration options for the farcaster-miniapp-monad-template project.

## Required Environment Variables

### Core Application Settings

```bash
# Base URL of your application
NEXT_PUBLIC_URL="https://your-domain.com"

# Backend service URL
NEXT_PUBLIC_NMLLAGENT_URL="https://your-backend.com"

# API token for secure communication
NMLLAGENT_API_TOKEN="your-secure-token"
```

### App Metadata (Optional but Recommended)

```bash
# App branding
NEXT_PUBLIC_APP_NAME="Your App Name"
NEXT_PUBLIC_APP_DESCRIPTION="Your app description"
NEXT_PUBLIC_APP_BUTTON_TEXT="Launch App"

# App categorization
NEXT_PUBLIC_APP_PRIMARY_CATEGORY="social"
NEXT_PUBLIC_APP_TAGS="tag1,tag2,tag3"
```

### Farcaster Integration

```bash
# Neynar Client ID (REQUIRED for notifications)
NEXT_PUBLIC_NEYNAR_CLIENT_ID="your-neynar-client-id"

# Account association (OPTIONAL - for domain verification)
# Generate using Farcaster's manifest tool instead of using seed phrase
ACCOUNT_ASSOCIATION='{"header":"...","payload":"...","signature":"..."}'
```

## Configuration Architecture

### Unified Configuration System

The app uses a unified configuration system located in `/lib/app-config.ts` that:

1. **Centralizes all configuration** - No more scattered hardcoded values
2. **Provides type safety** - TypeScript interfaces for all config objects
3. **Handles environment validation** - Checks for required variables
4. **Generates consistent manifests** - Same config used everywhere

### Key Functions

- `APP_CONFIG` - Central configuration object
- `getWebhookUrl()` - Generates webhook URL based on Neynar settings
- `generateFarcasterManifest()` - Creates Farcaster manifest
- `generateFrameEmbedMetadata()` - Creates frame embed metadata
- `validateEnvironment()` - Checks required environment variables

## Environment Setup by Stage

### Development

```bash
NEXT_PUBLIC_URL="http://localhost:3001"
NEXT_PUBLIC_NMLLAGENT_URL="http://localhost:3000"
NMLLAGENT_API_TOKEN="dev-token"
```

### Staging

```bash
NEXT_PUBLIC_URL="https://staging.your-domain.com"
NEXT_PUBLIC_NMLLAGENT_URL="https://staging-api.your-domain.com"
NMLLAGENT_API_TOKEN="staging-token"
```

### Production

```bash
NEXT_PUBLIC_URL="https://your-domain.com"
NEXT_PUBLIC_NMLLAGENT_URL="https://api.your-domain.com"
NMLLAGENT_API_TOKEN="production-token"
NEXT_PUBLIC_NEYNAR_CLIENT_ID="your-production-client-id"
```

## Backend Configuration (nmllagent)

Your nmllagent backend should have these environment variables:

```bash
# Frontend URL for notifications
FARCASTER_MINIAPP_URL="https://your-domain.com"

# Neynar configuration (same as frontend)
NEYNAR_API_KEY="your-neynar-api-key"
NEYNAR_CLIENT_ID="your-neynar-client-id"

# API token (same as frontend)
NMLLAGENT_API_TOKEN="your-secure-token"
```

## Deployment Checklist

### Before Deployment

- [ ] Set `NEXT_PUBLIC_URL` to your production domain
- [ ] Configure `NMLLAGENT_API_TOKEN` (same value in frontend and backend)
- [ ] Set up Neynar API key and client ID
- [ ] Configure Farcaster account association (SEED_PHRASE + FARCASTER_FID)
- [ ] Update app metadata (name, description, tags)

### After Deployment

- [ ] Verify manifest at `https://your-domain.com/.well-known/farcaster.json`
- [ ] Test notification functionality
- [ ] Verify frame embed metadata
- [ ] Check environment validation in browser console

## Troubleshooting

### Common Issues

1. **Notifications not working**
   - Check NEYNAR_API_KEY and NEYNAR_CLIENT_ID
   - Verify webhook URL in manifest
   - Ensure backend has FARCASTER_MINIAPP_URL set

2. **Domain verification failed**
   - Check SEED_PHRASE and FARCASTER_FID
   - Verify account association in manifest
   - Use Farcaster's manifest tool to generate association

3. **API communication errors**
   - Verify NMLLAGENT_API_TOKEN matches between frontend and backend
   - Check NEXT_PUBLIC_NMLLAGENT_URL is accessible
   - Ensure CORS is configured on backend

### Debug Commands

```bash
# Check environment validation
npm run dev
# Look for validation errors in browser console

# Verify manifest
curl https://your-domain.com/.well-known/farcaster.json

# Test API connection
curl -H "Authorization: Bearer your-token" https://your-backend.com/health
```

## Migration from Old Configuration

If you're migrating from the old scattered configuration:

1. **Update imports** - Replace direct constants with unified config functions
2. **Consolidate environment variables** - Use the new standardized names
3. **Remove hardcoded values** - Replace with environment variables
4. **Test thoroughly** - Verify all functionality works with new config

## Security Notes

- Never commit `.env` files to version control
- Use different API tokens for different environments
- Rotate tokens regularly
- Keep Neynar API keys secure
- Use HTTPS in production for all URLs
