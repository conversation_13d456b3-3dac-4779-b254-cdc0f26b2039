{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "ignore": []}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"useTemplate": {"level": "error", "fix": "safe"}, "useSelfClosingElements": {"level": "error", "fix": "safe"}, "noUnusedTemplateLiteral": {"level": "error", "fix": "safe"}, "noNonNullAssertion": {"level": "warn"}}}}, "javascript": {"formatter": {"quoteStyle": "single", "semicolons": "asNeeded"}}}