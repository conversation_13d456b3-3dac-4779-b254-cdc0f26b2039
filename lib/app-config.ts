/**
 * Unified Application Configuration
 * 
 * This file centralizes all app configuration to avoid duplication and ensure consistency
 * across different parts of the application (manifest, pages, API routes, etc.)
 */

import { APP_URL } from './constants'

// --- Core App Metadata ---
export const APP_CONFIG = {
  // Basic app information
  name: process.env.NEXT_PUBLIC_APP_NAME || "NoMoreLonelyLife",
  description: process.env.NEXT_PUBLIC_APP_DESCRIPTION || "AI Matching Agent for meaningful connections",
  version: "0.0.1",
  
  // Visual branding
  buttonTitle: process.env.NEXT_PUBLIC_APP_BUTTON_TEXT || "Let me make a wish!",
  primaryCategory: process.env.NEXT_PUBLIC_APP_PRIMARY_CATEGORY || "social",
  tags: process.env.NEXT_PUBLIC_APP_TAGS?.split(',') || ["monad", "farcaster", "miniapp", "matching"],
  
  // Colors and theming
  splashBackgroundColor: "#141316", 
  
  // URLs and assets
  baseUrl: APP_URL,
  iconUrl: `${APP_URL}/images/icon.png`,
  homeUrl: APP_URL,
  imageUrl: `${APP_URL}/images/feed.png`,
  splashImageUrl: `${APP_URL}/images/splash.png`,
  
  // Integration settings
  neynar: {
    clientId: process.env.NEXT_PUBLIC_NEYNAR_CLIENT_ID,
  }
} as const

// --- Webhook URL Generation ---
export function getWebhookUrl(): string {
  const { neynar } = APP_CONFIG

  // Frontend only needs client ID to generate webhook URL
  // The actual API key is handled by the backend
  if (neynar.clientId) {
    return `https://api.neynar.com/f/app/${neynar.clientId}/event`
  }

  return `${APP_CONFIG.baseUrl}/api/webhook`
}

// --- Farcaster Manifest Generation ---
export interface FarcasterManifestConfig {
  accountAssociation?: {
    header: string
    payload: string
    signature: string
  }
  frame: {
    version: string
    name: string
    iconUrl: string
    homeUrl: string
    imageUrl: string
    screenshotUrls: string[]
    tags: string[]
    primaryCategory: string
    buttonTitle: string
    splashImageUrl: string
    splashBackgroundColor: string
    webhookUrl: string
  }
}

export function generateFarcasterManifest(accountAssociation?: FarcasterManifestConfig['accountAssociation']): FarcasterManifestConfig {
  const config: FarcasterManifestConfig = {
    frame: {
      version: APP_CONFIG.version,
      name: APP_CONFIG.name,
      iconUrl: APP_CONFIG.iconUrl,
      homeUrl: APP_CONFIG.homeUrl,
      imageUrl: APP_CONFIG.imageUrl,
      screenshotUrls: [],
      tags: APP_CONFIG.tags,
      primaryCategory: APP_CONFIG.primaryCategory,
      buttonTitle: APP_CONFIG.buttonTitle,
      splashImageUrl: APP_CONFIG.splashImageUrl,
      splashBackgroundColor: APP_CONFIG.splashBackgroundColor,
      webhookUrl: getWebhookUrl(),
    }
  }
  
  if (accountAssociation) {
    config.accountAssociation = accountAssociation
  }
  
  return config
}

// --- Frame Embed Metadata Generation ---
export interface FrameEmbedMetadata {
  version: string
  imageUrl: string
  button: {
    title: string
    action: {
      type: string
      name: string
      url: string
      splashImageUrl: string
      splashBackgroundColor: string
    }
  }
}

export function generateFrameEmbedMetadata(): FrameEmbedMetadata {
  return {
    version: 'next',
    imageUrl: APP_CONFIG.imageUrl,
    button: {
      title: APP_CONFIG.buttonTitle,
      action: {
        type: 'launch_frame',
        name: APP_CONFIG.name,
        url: APP_CONFIG.baseUrl,
        splashImageUrl: APP_CONFIG.splashImageUrl,
        splashBackgroundColor: APP_CONFIG.splashBackgroundColor,
      },
    },
  }
}

// --- Build/Deploy Script Configuration ---
export interface BuildScriptConfig {
  domain: string
  frameName?: string
  buttonText?: string
  neynarClientId?: string
}

export function generateBuildManifest(config: BuildScriptConfig) {
  const { domain, frameName, buttonText, neynarClientId } = config
  
  const webhookUrl = neynarClientId 
    ? `https://api.neynar.com/f/app/${neynarClientId}/event`
    : `https://${domain}/api/webhook`
  
  return {
    frame: {
      version: "1",
      name: frameName || APP_CONFIG.name,
      iconUrl: `https://${domain}/icon.png`,
      homeUrl: `https://${domain}`,
      imageUrl: `https://${domain}/api/opengraph-image`,
      buttonTitle: buttonText || APP_CONFIG.buttonTitle,
      splashImageUrl: `https://${domain}/splash.png`,
      splashBackgroundColor: APP_CONFIG.splashBackgroundColor,
      webhookUrl,
      description: process.env.NEXT_PUBLIC_APP_DESCRIPTION || APP_CONFIG.description,
      primaryCategory: process.env.NEXT_PUBLIC_APP_PRIMARY_CATEGORY || APP_CONFIG.primaryCategory,
      tags: process.env.NEXT_PUBLIC_APP_TAGS?.split(',') || APP_CONFIG.tags,
    },
  }
}

// --- Environment Validation ---
export function validateEnvironment(): { isValid: boolean; missing: string[] } {
  const required = [
    'NEXT_PUBLIC_URL',
    'NEXT_PUBLIC_NMLLAGENT_URL', 
    'NMLLAGENT_API_TOKEN'
  ]
  
  const missing = required.filter(key => !process.env[key])
  
  return {
    isValid: missing.length === 0,
    missing
  }
}

// --- Configuration Summary for Debugging ---
export function getConfigSummary() {
  return {
    app: {
      name: APP_CONFIG.name,
      baseUrl: APP_CONFIG.baseUrl,
      version: APP_CONFIG.version,
    },
    neynar: {
      hasClientId: !!APP_CONFIG.neynar.clientId,
      webhookUrl: getWebhookUrl(),
    },
    environment: validateEnvironment(),
  }
}
