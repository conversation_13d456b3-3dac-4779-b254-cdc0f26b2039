import { APP_URL } from "@/lib/constants";
import {
  SendNotificationRequest,
  sendNotificationResponseSchema,
} from "@farcaster/miniapp-sdk";

type SendFrameNotificationResult =
  | {
      state: "error";
      error: unknown;
    }
  | { state: "no_token" }
  | { state: "rate_limit" }
  | { state: "success" };

/**
 * Send frame notification to a specific user
 * 
 * Note: This is a fallback function for direct notification sending.
 * In the current architecture, notifications are primarily handled by the backend (nmllagent)
 * using Neynar SDK, which is more reliable and doesn't require storing tokens on frontend.
 */
export async function sendFrameNotification({
  fid,
  title,
  body,
}: {
  fid: number;
  title: string;
  body: string;
}): Promise<SendFrameNotificationResult> {
  console.log('📱 [FRAME NOTIFICATION] Attempting to send notification to FID:', fid);
  
  // TODO: Get notification details from storage or API
  // In the current architecture, this would need to be retrieved from
  // the backend or local storage where notification tokens are stored
  const notificationDetails = { url: "", token: "" };

  if (!notificationDetails.url || !notificationDetails.token) {
    console.warn('⚠️ [FRAME NOTIFICATION] No notification token available for FID:', fid);
    console.warn('   This is expected if using backend-managed notifications (Neynar SDK)');
    return { state: "no_token" };
  }

  try {
    console.log('📡 [FRAME NOTIFICATION] Sending notification request...');
    
    const response = await fetch(notificationDetails.url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        notificationId: crypto.randomUUID(),
        title,
        body,
        targetUrl: APP_URL,
        tokens: [notificationDetails.token],
      } satisfies SendNotificationRequest),
    });

    const responseJson = await response.json();

    if (response.status === 200) {
      const responseBody = sendNotificationResponseSchema.safeParse(responseJson);
      if (responseBody.success === false) {
        // Malformed response
        console.error('❌ [FRAME NOTIFICATION] Malformed response:', responseBody.error.errors);
        return { state: "error", error: responseBody.error.errors };
      }

      if (responseBody.data.result.rateLimitedTokens.length) {
        // Rate limited
        console.warn('⚠️ [FRAME NOTIFICATION] Rate limited for FID:', fid);
        return { state: "rate_limit" };
      }

      console.log('✅ [FRAME NOTIFICATION] Notification sent successfully to FID:', fid);
      return { state: "success" };
    } else {
      // Error response
      console.error('❌ [FRAME NOTIFICATION] Error response:', responseJson);
      return { state: "error", error: responseJson };
    }
  } catch (error) {
    console.error('❌ [FRAME NOTIFICATION] Network error:', error);
    return { state: "error", error };
  }
}

/**
 * Trigger a refresh of chat history after receiving a notification
 * This function can be called when the app receives a notification
 * to automatically refresh the chat interface
 */
export function triggerChatRefresh() {
  console.log('🔄 [CHAT REFRESH] Triggering chat history refresh...');
  
  // Dispatch a custom event that the chat interface can listen to
  if (typeof window !== 'undefined') {
    const refreshEvent = new CustomEvent('farcaster-notification-received', {
      detail: { timestamp: Date.now() }
    });
    window.dispatchEvent(refreshEvent);
  }
}

/**
 * Setup notification listener for automatic chat refresh
 * Call this in your main app component to enable automatic refresh
 * when notifications are received
 */
export function setupNotificationListener() {
  if (typeof window === 'undefined') return;

  console.log('🎧 [NOTIFICATION LISTENER] Setting up notification listener...');
  
  const handleNotification = (event: CustomEvent) => {
    console.log('📨 [NOTIFICATION LISTENER] Notification received, refreshing chat...', event.detail);
    // The chat interface should listen to this event and refresh its history
  };

  window.addEventListener('farcaster-notification-received', handleNotification as EventListener);
  
  // Return cleanup function
  return () => {
    window.removeEventListener('farcaster-notification-received', handleNotification as EventListener);
  };
}
