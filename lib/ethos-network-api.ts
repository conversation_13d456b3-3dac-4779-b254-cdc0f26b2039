/**
 * Ethos Network API Service
 * Handles user data retrieval from Ethos Network v2 API
 * Supports Telegram, Farcaster, and extensible platform integration
 */

// TypeScript interfaces for Ethos Network API responses
export interface EthosUserStats {
  review: {
    received: {
      negative: number;
      neutral: number;
      positive: number;
    };
  };
  vouch: {
    given: {
      amountWeiTotal: number;
      count: number;
    };
    received: {
      amountWeiTotal: number;
      count: number;
    };
  };
}

export interface EthosUserData {
  id: number;
  profileId: number;
  displayName: string;
  username: string;
  avatarUrl: string;
  description: string;
  score: number;
  status: string;
  userkeys: string[];
  xpTotal: number;
  xpStreakDays: number;
  stats: EthosUserStats;
}

export interface EthosApiResponse {
  success: boolean;
  data?: EthosUserData[];
  error?: {
    code: string;
    message: string;
    issues?: any[];
  };
}

// Platform configuration for extensible design
interface PlatformConfig {
  apiEndpoint: string;
  idFieldName: string;
  requestBodyKey: string;
}

const PLATFORM_CONFIGS: Record<string, PlatformConfig> = {
  telegram: {
    apiEndpoint: 'https://api.ethos.network/api/v2/users/by/telegram',
    idFieldName: 'telegramIds',
    requestBodyKey: 'telegramIds'
  },
  farcaster: {
    apiEndpoint: 'https://api.ethos.network/api/v2/users/by/farcaster',
    idFieldName: 'farcasterIds', 
    requestBodyKey: 'farcasterIds'
  }
  // Future platforms can be easily added here
};

// Client identification header
const ETHOS_CLIENT_HEADER = 'NMLL@1.2.0';

/**
 * Fetch user data from Ethos Network API
 * @param platform - Platform name (telegram, farcaster, etc.)
 * @param userId - User ID for the platform
 * @returns Promise<EthosApiResponse>
 */
export async function fetchEthosUserData(
  platform: string, 
  userId: string
): Promise<EthosApiResponse> {
  try {
    // Normalize platform name for lookup
    const normalizedPlatform = platform.toLowerCase();
    
    // Check if platform is supported
    const platformConfig = PLATFORM_CONFIGS[normalizedPlatform];
    if (!platformConfig) {
      return {
        success: false,
        error: {
          code: 'UNSUPPORTED_PLATFORM',
          message: `Platform "${platform}" is not supported by Ethos Network API`,
          issues: []
        }
      };
    }

    // Prepare request body
    const requestBody = {
      [platformConfig.requestBodyKey]: [userId]
    };

    console.log(`🌐 [ETHOS] Fetching user data for ${platform} user: ${userId}`);
    
    // Make API request
    const response = await fetch(platformConfig.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Ethos-Client': ETHOS_CLIENT_HEADER
      },
      body: JSON.stringify(requestBody)
    });

    // Handle HTTP errors
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ [ETHOS] HTTP ${response.status}: ${errorText}`);
      
      return {
        success: false,
        error: {
          code: 'HTTP_ERROR',
          message: `HTTP ${response.status}: ${response.statusText}`,
          issues: [errorText]
        }
      };
    }

    // Parse response
    const responseData = await response.json();
    
    // Handle API error responses
    if (responseData.code && responseData.message) {
      console.error(`❌ [ETHOS] API Error: ${responseData.code} - ${responseData.message}`);
      return {
        success: false,
        error: {
          code: responseData.code,
          message: responseData.message,
          issues: responseData.issues || []
        }
      };
    }

    // Handle successful response
    if (Array.isArray(responseData) && responseData.length > 0) {
      console.log(`✅ [ETHOS] Successfully fetched data for ${platform} user: ${userId}`);
      return {
        success: true,
        data: responseData
      };
    }

    // Handle empty results
    console.warn(`⚠️ [ETHOS] No data found for ${platform} user: ${userId}`);
    return {
      success: false,
      error: {
        code: 'USER_NOT_FOUND',
        message: `No user data found for ${platform} user: ${userId}`,
        issues: []
      }
    };

  } catch (error) {
    console.error('❌ [ETHOS] Network or parsing error:', error);
    return {
      success: false,
      error: {
        code: 'NETWORK_ERROR',
        message: error instanceof Error ? error.message : 'Unknown network error',
        issues: []
      }
    };
  }
}

/**
 * Check if a platform is supported by Ethos Network API
 * @param platform - Platform name to check
 * @returns boolean
 */
export function isPlatformSupported(platform: string): boolean {
  return Object.keys(PLATFORM_CONFIGS).includes(platform.toLowerCase());
}

/**
 * Get list of supported platforms
 * @returns string[] - Array of supported platform names
 */
export function getSupportedPlatforms(): string[] {
  return Object.keys(PLATFORM_CONFIGS);
}

/**
 * Transform Ethos Network user data to UserProfile format
 * @param ethosData - Raw data from Ethos Network API
 * @param fallbackData - Fallback data from sidebar match info
 * @returns Transformed user profile data
 */
export function transformEthosDataToProfile(
  ethosData: EthosUserData,
  fallbackData?: {
    platform: string;
    realID: string;
    username: string;
    avatarUrl?: string;
  }
) {
  return {
    // Basic info - prioritize Ethos data
    platform: fallbackData?.platform || 'Unknown Platform',
    id: ethosData.id.toString(),
    profileId: ethosData.profileId.toString(),
    displayName: ethosData.displayName,
    username: ethosData.username,
    avatarUrl: ethosData.avatarUrl,
    description: ethosData.description,
    score: ethosData.score,
    stats: ethosData.stats,
    // Use fallback avatar if Ethos avatar is not available
    selfie: fallbackData?.avatarUrl || ethosData.avatarUrl
  };
}
