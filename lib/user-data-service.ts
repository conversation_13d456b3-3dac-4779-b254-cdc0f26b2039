/**
 * User Data Service
 * Handles user data fetching with Ethos Network API priority and fallback logic
 */

import {
  fetchEthosUserData,
  transformEthosDataToProfile,
  isPlatformSupported
} from './ethos-network-api';
import {
  UserDataFetchOptions,
  UserDataFetchResult,
  UserProfileData,
  FallbackUserData,
  MatchData
} from '@/types/user-profile';
import { profileCache } from './profile-cache';

/**
 * Main function to fetch user profile data
 * Priority: Ethos Network API -> Fallback to local data
 */
export async function fetchUserProfileData(
  options: UserDataFetchOptions
): Promise<UserDataFetchResult> {
  const { platform, realID, username, fallbackData } = options;

  console.log(`🔍 [USER-DATA] Fetching profile for ${platform} user: ${realID} (@${username})`);

  try {
    // Step 0: Check cache first
    const cachedEntry = profileCache.get(platform, realID);
    if (cachedEntry && !profileCache.shouldRefresh(platform, realID)) {
      console.log(`📦 [USER-DATA] Using cached data for ${username}`);
      return {
        success: true,
        data: cachedEntry.data,
        source: cachedEntry.source
      };
    }

    // Step 1: Try Ethos Network API if platform is supported
    if (isPlatformSupported(platform)) {
      console.log(`🌐 [USER-DATA] Platform ${platform} is supported, trying Ethos Network API...`);

      const ethosResult = await fetchEthosUserData(platform, realID);

      if (ethosResult.success && ethosResult.data && ethosResult.data.length > 0) {
        const ethosUser = ethosResult.data[0]; // Take first result
        const transformedData = transformEthosDataToProfile(ethosUser, {
          platform,
          realID,
          username,
          avatarUrl: fallbackData?.avatarUrl
        });

        // Cache the result
        profileCache.set(platform, realID, transformedData, 'ethos');

        console.log(`✅ [USER-DATA] Successfully fetched from Ethos Network for ${username}`);
        return {
          success: true,
          data: transformedData,
          source: 'ethos'
        };
      } else {
        console.warn(`⚠️ [USER-DATA] Ethos Network API failed: ${ethosResult.error?.message}`);
      }
    } else {
      console.log(`ℹ️ [USER-DATA] Platform ${platform} not supported by Ethos Network, using fallback`);
    }

    // Step 2: Fallback to local data
    console.log(`🔄 [USER-DATA] Using fallback data for ${username}`);
    const fallbackProfile = createFallbackProfile({
      platform,
      realID,
      username,
      avatarUrl: fallbackData?.avatarUrl,
      timestamp: fallbackData?.timestamp
    });

    // Cache the fallback result
    profileCache.set(platform, realID, fallbackProfile, 'fallback');

    return {
      success: true,
      data: fallbackProfile,
      source: 'fallback'
    };

  } catch (error) {
    console.error(`❌ [USER-DATA] Error fetching profile for ${username}:`, error);
    
    // Even on error, try to provide fallback data
    const fallbackProfile = createFallbackProfile({
      platform,
      realID,
      username,
      avatarUrl: fallbackData?.avatarUrl,
      timestamp: fallbackData?.timestamp
    });

    return {
      success: false,
      data: fallbackProfile,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      source: 'error'
    };
  }
}

/**
 * Create fallback profile data from match information
 */
function createFallbackProfile(data: FallbackUserData): UserProfileData {
  const { platform, realID, username, avatarUrl, timestamp } = data;

  // Generate a simple avatar URL if none provided
  const fallbackAvatarUrl = avatarUrl || generateAvatarUrl(username);

  return {
    platform: platform, // Keep original format for API calls
    id: realID,
    profileId: realID,
    displayName: username,
    username: username,
    avatarUrl: fallbackAvatarUrl,
    description: `${capitalizeFirst(platform)} user`,
    score: 0,
    stats: {
      review: {
        received: {
          negative: 0,
          neutral: 0,
          positive: 0
        }
      },
      vouch: {
        given: {
          amountWeiTotal: 0,
          count: 0
        },
        received: {
          amountWeiTotal: 0,
          count: 0
        }
      }
    },
    selfie: fallbackAvatarUrl
  };
}

/**
 * Generate a simple avatar URL using a service like DiceBear or similar
 */
function generateAvatarUrl(username: string): string {
  // Using DiceBear API for consistent avatar generation
  const seed = encodeURIComponent(username);
  return `https://api.dicebear.com/7.x/initials/svg?seed=${seed}&backgroundColor=C7FF02,9AE600,7ACC00`;
}

/**
 * Capitalize first letter of a string
 */
function capitalizeFirst(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Convert MatchData to UserDataFetchOptions
 */
export function matchDataToFetchOptions(match: MatchData): UserDataFetchOptions {
  return {
    platform: match.platform,
    realID: match.realID,
    username: match.matchedUsername,
    fallbackData: {
      platform: match.platform,
      realID: match.realID,
      username: match.matchedUsername,
      timestamp: match.timestamp
    }
  };
}

/**
 * Validate user data fetch options
 */
export function validateFetchOptions(options: UserDataFetchOptions): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!options.platform || options.platform.trim() === '') {
    errors.push('Platform is required');
  }

  if (!options.realID || options.realID.trim() === '') {
    errors.push('Real ID is required');
  }

  if (!options.username || options.username.trim() === '') {
    errors.push('Username is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Get platform display name and icon
 */
export function getPlatformInfo(platform: string): {
  displayName: string;
  icon: string;
  color: string;
} {
  const normalizedPlatform = platform.toLowerCase();
  
  switch (normalizedPlatform) {
    case 'telegram':
      return {
        displayName: 'Telegram',
        icon: '💙',
        color: 'text-blue-400'
      };
    case 'farcaster':
      return {
        displayName: 'Farcaster',
        icon: '🟣',
        color: 'text-purple-400'
      };
    default:
      return {
        displayName: capitalizeFirst(platform),
        icon: '👤',
        color: 'text-gray-400'
      };
  }
}

/**
 * Format timestamp for display
 */
export function formatMatchTimestamp(timestamp: number): string {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
  
  if (diffInHours < 1) {
    return 'Just now';
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)}h ago`;
  } else if (diffInHours < 24 * 7) {
    const days = Math.floor(diffInHours / 24);
    return `${days}d ago`;
  } else {
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  }
}

/**
 * Check if user data is from Ethos Network
 */
export function isEthosData(data: UserProfileData): boolean {
  // Ethos data typically has numeric IDs and more complete stats
  return !!(
    data.id && 
    !isNaN(Number(data.id)) && 
    data.stats && 
    (data.stats.review.received.positive > 0 || 
     data.stats.review.received.neutral > 0 || 
     data.stats.review.received.negative > 0 ||
     data.stats.vouch.received.count > 0 ||
     data.stats.vouch.given.count > 0)
  );
}
