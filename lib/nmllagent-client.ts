/**
 * NMLLAgent API Client
 * Lightweight client for communicating with nmllagent backend
 * Uses unified Farcaster Quick Auth for secure authentication
 */

import { farcasterAuth } from './farcaster-auth'

export interface Message {
  id: string
  content: string
  type: 'user' | 'system'
  timestamp: number
  completed: boolean
  newSection: boolean
}

export interface SendMessageRequest {
  text: string
  fid: string
  username?: string
}

export interface SendMessageResponse {
  response: string
  success: boolean
  aiResponseId?: string // Optional AI response ID from backend
}

export interface GetHistoryResponse {
  history: Message[]
  success: boolean
}

export class NMLLAgentClient {
  private baseUrl: string

  constructor(baseUrl?: string) {
    // Use proxy endpoints instead of direct nmllagent connection
    // This avoids CORS issues when accessing through Cloudflare tunnel
    this.baseUrl = baseUrl || ''
  }

  /**
   * Send a message to nmllagent and get AI response
   * Uses Farcaster Quick Auth for authentication
   */
  async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    const timestamp = new Date().toISOString()
    console.log(`\n🚀 [CLIENT ${timestamp}] === SEND MESSAGE START ===`)

    try {
      console.log('🔧 [CLIENT STEP 1] Environment check...')
      console.log('🌐 [CLIENT STEP 1] Base URL:', this.baseUrl || 'EMPTY (using relative paths)')
      console.log('🌐 [CLIENT STEP 1] Window location:', typeof window !== 'undefined' ? window.location.href : 'SERVER_SIDE')

      const fullUrl = `${this.baseUrl}/api/nmllagent/message`
      console.log(`🔗 [CLIENT STEP 2] Sending message to: ${fullUrl}`)
      console.log('📤 [CLIENT STEP 2] Request payload:', JSON.stringify(request, null, 2))

      console.log('🔒 [CLIENT STEP 3] Using Farcaster Quick Auth...')
      console.log('📡 [CLIENT STEP 3] Making authenticated fetch request...')
      const startTime = Date.now()

      // Use Farcaster Quick Auth for automatic token management
      const response = await farcasterAuth.authenticatedFetch(fullUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      })

      const endTime = Date.now()
      console.log(`⏱️ [CLIENT STEP 3] Fetch completed in ${endTime - startTime}ms`)
      console.log(`📊 [CLIENT STEP 3] Response status: ${response.status} ${response.statusText}`)
      console.log(`📊 [CLIENT STEP 3] Response headers:`, Object.fromEntries(response.headers.entries()))

      if (!response.ok) {
        console.log('❌ [CLIENT STEP 4] Response not OK, reading error...')
        const errorText = await response.text()
        console.error(`❌ [CLIENT STEP 4] HTTP error details:`, {
          status: response.status,
          statusText: response.statusText,
          url: fullUrl,
          errorBody: errorText
        })
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`)
      }

      console.log('✅ [CLIENT STEP 4] Response OK, parsing JSON...')
      const data = await response.json()
      console.log('✅ [CLIENT STEP 4] Response data:', JSON.stringify(data, null, 2))

      console.log('🎉 [CLIENT STEP 5] Success! Returning data to UI')
      console.log(`🏁 [CLIENT ${timestamp}] === SEND MESSAGE END ===\n`)

      return data
    } catch (error) {
      const errorDetails = error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : {
        name: 'Unknown',
        message: String(error),
        stack: undefined
      }

      console.error(`💥 [CLIENT ERROR] Send message error at ${timestamp}:`, {
        ...errorDetails,
        baseUrl: this.baseUrl
      })
      console.log(`🏁 [CLIENT ${timestamp}] === SEND MESSAGE END (ERROR) ===\n`)

      return {
        response: `Sorry, I am having trouble connecting to the AI service. Error: ${errorDetails.message}`,
        success: false,
      }
    }
  }

  /**
   * Get chat history for authenticated user
   * Uses Farcaster Quick Auth for authentication
   * FID is extracted from JWT token on backend for security
   */
  async getHistory(): Promise<GetHistoryResponse> {
    const timestamp = new Date().toISOString()
    console.log(`\n📜 [CLIENT ${timestamp}] === GET HISTORY START ===`)

    try {
      console.log('🔧 [CLIENT STEP 1] Environment check...')
      console.log('🌐 [CLIENT STEP 1] Base URL:', this.baseUrl || 'EMPTY (using relative paths)')

      // 🔒 SECURITY: No FID in URL - extracted from JWT token on backend
      const url = `${this.baseUrl}/api/nmllagent/history`
      console.log(`🔗 [CLIENT STEP 2] Getting history from: ${url}`)

      console.log('🔒 [CLIENT STEP 3] Using Farcaster Quick Auth...')
      console.log('📡 [CLIENT STEP 3] Making authenticated fetch request...')
      const startTime = Date.now()

      // Use Farcaster Quick Auth for automatic token management
      const response = await farcasterAuth.authenticatedFetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const endTime = Date.now()
      console.log(`⏱️ [CLIENT STEP 3] Fetch completed in ${endTime - startTime}ms`)
      console.log(`📊 [CLIENT STEP 3] Response status: ${response.status} ${response.statusText}`)
      console.log(`📊 [CLIENT STEP 3] Response headers:`, Object.fromEntries(response.headers.entries()))

      if (!response.ok) {
        console.log('❌ [CLIENT STEP 4] Response not OK, reading error...')
        const errorText = await response.text()
        console.error(`❌ [CLIENT STEP 4] HTTP error details:`, {
          status: response.status,
          statusText: response.statusText,
          url: url,
          errorBody: errorText
        })
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`)
      }

      console.log('✅ [CLIENT STEP 4] Response OK, parsing JSON...')
      const data = await response.json()
      console.log(`✅ [CLIENT STEP 4] History data received:`, {
        success: data.success,
        historyLength: data.history?.length || 0,
        sampleMessages: data.history?.slice(0, 2) // Show first 2 for debugging
      })

      console.log('🎉 [CLIENT STEP 5] Success! Returning history to UI')
      console.log(`🏁 [CLIENT ${timestamp}] === GET HISTORY END ===\n`)

      return data
    } catch (error) {
      const errorDetails = error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : {
        name: 'Unknown',
        message: String(error),
        stack: undefined
      }

      console.error(`💥 [CLIENT ERROR] Get history error at ${timestamp}:`, {
        ...errorDetails,
        baseUrl: this.baseUrl
      })
      console.log(`🏁 [CLIENT ${timestamp}] === GET HISTORY END (ERROR) ===\n`)

      return {
        history: [],
        success: false,
      }
    }
  }
}

// Default client instance
export const nmllagentClient = new NMLLAgentClient()
