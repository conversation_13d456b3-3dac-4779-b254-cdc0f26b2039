import { notificationDetailsSchema } from "@farcaster/miniapp-sdk";
import { z } from "zod";

// Infer the type from the schema
type FrameNotificationDetails = z.infer<typeof notificationDetailsSchema>;

/**
 * Handle notification setup from addMiniApp result
 *
 * Note: With Neynar SDK backend, we don't need to store anything locally.
 * Neynar automatically manages all notification tokens server-side.
 * This function just logs the setup status for debugging.
 */
export function handleNotificationSetup(
  fid: number,
  notificationDetails: FrameNotificationDetails | null
): boolean {
  if (notificationDetails && notificationDetails.url && notificationDetails.token) {
    console.log('✅ [NOTIFICATION SETUP] User enabled notifications for FID:', fid);
    console.log('📱 [NOTIFICATION SETUP] Notification details received (managed by Neynar)');
    return true;
  } else {
    console.warn('⚠️ [NOTIFICATION SETUP] User declined notifications or invalid details for FID:', fid);
    return false;
  }
}
