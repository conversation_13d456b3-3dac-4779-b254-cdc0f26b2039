/**
 * 🔒 Unified Farcaster Authentication Service
 * 
 * Uses official @farcaster/miniapp-sdk Quick Auth for secure authentication
 * Replaces the previous mixed JWT + API token approach
 * 
 * Features:
 * - Official Farcaster Quick Auth integration
 * - Automatic token management and refresh
 * - Centralized authentication for all API calls
 * - Engineering minimization approach
 */

import { sdk } from '@farcaster/miniapp-sdk'
import React from 'react'

export interface FarcasterUser {
  fid: number
  username?: string
  displayName?: string
  pfpUrl?: string
  bio?: string
  verifications?: string[]
}

export interface AuthState {
  isAuthenticated: boolean
  isLoading: boolean
  user: FarcasterUser | null
  token: string | null
  error: string | null
}

/**
 * 🔒 Unified Farcaster Authentication Service
 * 
 * Handles all authentication using official Quick Auth
 */
export class FarcasterAuthService {
  private static instance: FarcasterAuthService
  private authState: AuthState = {
    isAuthenticated: false,
    isLoading: false,
    user: null,
    token: null,
    error: null
  }
  private listeners: ((state: AuthState) => void)[] = []

  private constructor() {}

  static getInstance(): FarcasterAuthService {
    if (!FarcasterAuthService.instance) {
      FarcasterAuthService.instance = new FarcasterAuthService()
    }
    return FarcasterAuthService.instance
  }

  /**
   * Subscribe to authentication state changes
   */
  subscribe(listener: (state: AuthState) => void): () => void {
    this.listeners.push(listener)
    return () => {
      const index = this.listeners.indexOf(listener)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }

  /**
   * Notify all listeners of state changes
   */
  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.authState))
  }

  /**
   * Update authentication state
   */
  private updateState(updates: Partial<AuthState>) {
    this.authState = { ...this.authState, ...updates }
    this.notifyListeners()
  }

  /**
   * Get current authentication state
   */
  getState(): AuthState {
    return { ...this.authState }
  }

  /**
   * 🚀 Initialize authentication using Quick Auth
   */
  async initialize(): Promise<void> {
    this.updateState({ isLoading: true, error: null })

    try {
      console.log('🔒 [FARCASTER AUTH] Initializing Quick Auth...')

      // Get Farcaster context first
      const context = await sdk.context
      if (!context?.user?.fid) {
        throw new Error('No Farcaster user context available')
      }

      console.log('🔒 [FARCASTER AUTH] Context verified for FID:', context.user.fid)

      // Get Quick Auth token
      const { token } = await sdk.quickAuth.getToken()
      console.log('🔒 [FARCASTER AUTH] Quick Auth token obtained')

      // Extract user info from context
      const user: FarcasterUser = {
        fid: context.user.fid,
        username: context.user.username,
        displayName: context.user.displayName,
        pfpUrl: context.user.pfpUrl,
        // Note: bio and verifications may not be available in context
        bio: undefined,
        verifications: undefined
      }

      this.updateState({
        isAuthenticated: true,
        isLoading: false,
        user,
        token,
        error: null
      })

      console.log('✅ [FARCASTER AUTH] Authentication successful for FID:', user.fid)

    } catch (error) {
      console.error('❌ [FARCASTER AUTH] Authentication failed:', error)
      this.updateState({
        isAuthenticated: false,
        isLoading: false,
        user: null,
        token: null,
        error: error instanceof Error ? error.message : 'Authentication failed'
      })
      throw error
    }
  }

  /**
   * 🔄 Refresh authentication token
   */
  async refreshToken(): Promise<void> {
    if (!this.authState.isAuthenticated) {
      throw new Error('Not authenticated')
    }

    try {
      console.log('🔄 [FARCASTER AUTH] Refreshing token...')
      const { token } = await sdk.quickAuth.getToken()
      
      this.updateState({ token })
      console.log('✅ [FARCASTER AUTH] Token refreshed successfully')
    } catch (error) {
      console.error('❌ [FARCASTER AUTH] Token refresh failed:', error)
      // If refresh fails, clear authentication
      this.signOut()
      throw error
    }
  }

  /**
   * 🚪 Sign out
   */
  signOut(): void {
    console.log('🚪 [FARCASTER AUTH] Signing out...')
    this.updateState({
      isAuthenticated: false,
      isLoading: false,
      user: null,
      token: null,
      error: null
    })
  }

  /**
   * 🔐 Get authentication headers for API requests
   */
  getAuthHeaders(): Record<string, string> {
    if (!this.authState.isAuthenticated || !this.authState.token) {
      throw new Error('User not authenticated')
    }

    return {
      'Authorization': `Bearer ${this.authState.token}`,
      'Content-Type': 'application/json'
    }
  }

  /**
   * 📡 Make authenticated fetch request using Quick Auth
   */
  async authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
    if (!this.authState.isAuthenticated) {
      throw new Error('User not authenticated')
    }

    try {
      // Use sdk.quickAuth.fetch for automatic token management
      return await sdk.quickAuth.fetch(url, options)
    } catch (error) {
      // If request fails due to auth, try to refresh token once
      if (error instanceof Error && error.message.includes('401')) {
        console.log('🔄 [FARCASTER AUTH] Attempting token refresh due to 401...')
        try {
          await this.refreshToken()
          // Retry with new token
          return await sdk.quickAuth.fetch(url, options)
        } catch (refreshError) {
          console.error('❌ [FARCASTER AUTH] Token refresh failed, signing out')
          this.signOut()
          throw refreshError
        }
      }
      throw error
    }
  }
}

/**
 * 🎯 Convenience function to get the auth service instance
 */
export const farcasterAuth = FarcasterAuthService.getInstance()

/**
 * 🔒 React Hook for Farcaster Authentication
 */
export function useFarcasterAuth() {
  const [state, setState] = React.useState<AuthState>(farcasterAuth.getState())

  React.useEffect(() => {
    const unsubscribe = farcasterAuth.subscribe(setState)
    return unsubscribe
  }, [])

  const initialize = React.useCallback(async () => {
    await farcasterAuth.initialize()
  }, [])

  const refreshToken = React.useCallback(async () => {
    await farcasterAuth.refreshToken()
  }, [])

  const signOut = React.useCallback(() => {
    farcasterAuth.signOut()
  }, [])

  const getAuthHeaders = React.useCallback(() => {
    return farcasterAuth.getAuthHeaders()
  }, [])

  const authenticatedFetch = React.useCallback(async (url: string, options?: RequestInit) => {
    return farcasterAuth.authenticatedFetch(url, options)
  }, [])

  return {
    ...state,
    initialize,
    refreshToken,
    signOut,
    getAuthHeaders,
    authenticatedFetch
  }
}
