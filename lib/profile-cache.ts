/**
 * Simple in-memory cache for user profile data
 * Helps reduce API calls and improve performance
 */

import { UserProfileData } from '@/types/user-profile';

interface CacheEntry {
  data: UserProfileData;
  timestamp: number;
  source: 'ethos' | 'fallback';
}

class ProfileCache {
  private cache = new Map<string, CacheEntry>();
  private readonly TTL = 5 * 60 * 1000; // 5 minutes cache TTL
  private readonly MAX_SIZE = 100; // Maximum cache entries

  /**
   * Generate cache key from platform and realID
   */
  private generateKey(platform: string, realID: string): string {
    return `${platform.toLowerCase()}:${realID}`;
  }

  /**
   * Check if cache entry is still valid
   */
  private isValid(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp < this.TTL;
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp >= this.TTL) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Ensure cache doesn't exceed maximum size
   */
  private enforceMaxSize(): void {
    if (this.cache.size <= this.MAX_SIZE) return;

    // Remove oldest entries
    const entries = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.timestamp - b.timestamp);
    
    const toRemove = entries.slice(0, this.cache.size - this.MAX_SIZE);
    toRemove.forEach(([key]) => this.cache.delete(key));
  }

  /**
   * Get cached profile data
   */
  get(platform: string, realID: string): CacheEntry | null {
    this.cleanup();
    
    const key = this.generateKey(platform, realID);
    const entry = this.cache.get(key);
    
    if (entry && this.isValid(entry)) {
      console.log(`📦 [CACHE] Hit for ${platform}:${realID}`);
      return entry;
    }
    
    if (entry) {
      // Remove expired entry
      this.cache.delete(key);
    }
    
    console.log(`🔍 [CACHE] Miss for ${platform}:${realID}`);
    return null;
  }

  /**
   * Set cached profile data
   */
  set(
    platform: string, 
    realID: string, 
    data: UserProfileData, 
    source: 'ethos' | 'fallback'
  ): void {
    this.cleanup();
    this.enforceMaxSize();
    
    const key = this.generateKey(platform, realID);
    const entry: CacheEntry = {
      data,
      timestamp: Date.now(),
      source
    };
    
    this.cache.set(key, entry);
    console.log(`💾 [CACHE] Stored ${platform}:${realID} from ${source}`);
  }

  /**
   * Clear specific entry
   */
  delete(platform: string, realID: string): boolean {
    const key = this.generateKey(platform, realID);
    return this.cache.delete(key);
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    this.cache.clear();
    console.log('🗑️ [CACHE] Cleared all entries');
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number;
    maxSize: number;
    ttl: number;
    entries: Array<{
      key: string;
      age: number;
      source: string;
    }>;
  } {
    this.cleanup();
    
    const now = Date.now();
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      age: now - entry.timestamp,
      source: entry.source
    }));

    return {
      size: this.cache.size,
      maxSize: this.MAX_SIZE,
      ttl: this.TTL,
      entries
    };
  }

  /**
   * Check if data should be refreshed (for Ethos data older than 1 minute)
   */
  shouldRefresh(platform: string, realID: string): boolean {
    const entry = this.cache.get(this.generateKey(platform, realID));
    if (!entry) return true;
    
    // Always refresh fallback data, refresh Ethos data after 1 minute
    if (entry.source === 'fallback') return true;
    
    const age = Date.now() - entry.timestamp;
    return age > 60 * 1000; // 1 minute for Ethos data
  }
}

// Export singleton instance
export const profileCache = new ProfileCache();

// Export cache statistics for debugging
export const getCacheStats = () => profileCache.getStats();

// Export cache clear function for testing
export const clearProfileCache = () => profileCache.clear();
