export const MESSAGE_EXPIRATION_TIME = 1000 * 60 * 60 * 24 * 30; // 30 day

// Helper function to ensure URL has proper protocol
function ensureHttpsUrl(url: string): string {
  if (!url) return url;

  // If URL already has protocol, return as is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // For production/Vercel, always use https
  if (process.env.NODE_ENV === 'production' || process.env.VERCEL_URL) {
    return `https://${url}`;
  }

  // For development, use http
  return `http://${url}`;
}

// Dynamic APP_URL that works in both development and production
export const APP_URL = (() => {
  // If NEXT_PUBLIC_URL is explicitly set, use it (with protocol check)
  if (process.env.NEXT_PUBLIC_URL) {
    return ensureHttpsUrl(process.env.NEXT_PUBLIC_URL);
  }

  // In production (Vercel), try to construct URL from Vercel environment variables
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }

  // Fallback for development
  if (process.env.NODE_ENV === 'development') {
    return 'http://localhost:3001'; // Match the dev port in package.json
  }

  // If none of the above work, throw an error
  throw new Error("Unable to determine APP_URL. Please set NEXT_PUBLIC_URL environment variable.");
})();
