/**
 * Integration tests for User Profile functionality
 * Tests the complete flow from match data to profile display
 */

import { 
  fetchUserProfileData, 
  matchDataToFetchOptions, 
  validateFetchOptions,
  getPlatformInfo,
  formatMatchTimestamp 
} from '../user-data-service';
import { fetchEthosUserData, isPlatformSupported } from '../ethos-network-api';
import { MatchData } from '@/types/user-profile';

// Mock data for testing
const mockTelegramMatch: MatchData = {
  id: 'match-123456-telegram-0',
  username: 'testuser',
  matchedUsername: 'alice_crypto',
  postMessage: '',
  timestamp: Date.now() - 3600000, // 1 hour ago
  platform: 'telegram',
  realID: '123456789'
};

const mockFarcasterMatch: MatchData = {
  id: 'match-12345-farcaster-0',
  username: 'testuser',
  matchedUsername: 'bob_defi',
  postMessage: '',
  timestamp: Date.now() - 86400000, // 1 day ago
  platform: 'farcaster',
  realID: '12345'
};

const mockUnsupportedMatch: MatchData = {
  id: 'match-999-discord-0',
  username: 'testuser',
  matchedUsername: 'charlie_nft',
  postMessage: '',
  timestamp: Date.now() - 604800000, // 1 week ago
  platform: 'discord',
  realID: '999888777'
};

describe('User Profile Integration', () => {
  describe('Platform Support', () => {
    test('should identify supported platforms correctly', () => {
      expect(isPlatformSupported('telegram')).toBe(true);
      expect(isPlatformSupported('farcaster')).toBe(true);
      expect(isPlatformSupported('discord')).toBe(false);
      expect(isPlatformSupported('TELEGRAM')).toBe(true); // Case insensitive
    });

    test('should get platform info correctly', () => {
      const telegramInfo = getPlatformInfo('telegram');
      expect(telegramInfo.displayName).toBe('Telegram');
      expect(telegramInfo.icon).toBe('💙');
      expect(telegramInfo.color).toBe('text-blue-400');

      const farcasterInfo = getPlatformInfo('farcaster');
      expect(farcasterInfo.displayName).toBe('Farcaster');
      expect(farcasterInfo.icon).toBe('🟣');
      expect(farcasterInfo.color).toBe('text-purple-400');

      const unknownInfo = getPlatformInfo('unknown');
      expect(unknownInfo.displayName).toBe('Unknown');
      expect(unknownInfo.icon).toBe('👤');
      expect(unknownInfo.color).toBe('text-gray-400');
    });
  });

  describe('Data Conversion', () => {
    test('should convert match data to fetch options correctly', () => {
      const options = matchDataToFetchOptions(mockTelegramMatch);
      
      expect(options.platform).toBe('telegram');
      expect(options.realID).toBe('123456789');
      expect(options.username).toBe('alice_crypto');
      expect(options.fallbackData).toEqual({
        platform: 'telegram',
        realID: '123456789',
        username: 'alice_crypto',
        timestamp: mockTelegramMatch.timestamp
      });
    });

    test('should validate fetch options correctly', () => {
      const validOptions = matchDataToFetchOptions(mockTelegramMatch);
      const validation = validateFetchOptions(validOptions);
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);

      const invalidOptions = {
        platform: '',
        realID: '',
        username: '',
        fallbackData: undefined
      };
      const invalidValidation = validateFetchOptions(invalidOptions);
      
      expect(invalidValidation.isValid).toBe(false);
      expect(invalidValidation.errors).toContain('Platform is required');
      expect(invalidValidation.errors).toContain('Real ID is required');
      expect(invalidValidation.errors).toContain('Username is required');
    });
  });

  describe('Timestamp Formatting', () => {
    test('should format timestamps correctly', () => {
      const now = Date.now();
      
      // Just now
      expect(formatMatchTimestamp(now - 30000)).toBe('Just now'); // 30 seconds ago
      
      // Hours ago
      expect(formatMatchTimestamp(now - 3600000)).toBe('1h ago'); // 1 hour ago
      expect(formatMatchTimestamp(now - 7200000)).toBe('2h ago'); // 2 hours ago
      
      // Days ago
      expect(formatMatchTimestamp(now - 86400000)).toBe('1d ago'); // 1 day ago
      expect(formatMatchTimestamp(now - 172800000)).toBe('2d ago'); // 2 days ago
      
      // Older dates should show month/day
      const oldDate = now - 604800000; // 1 week ago
      const formatted = formatMatchTimestamp(oldDate);
      expect(formatted).toMatch(/\w{3} \d{1,2}/); // Format like "Jan 15"
    });
  });

  describe('Profile Data Fetching', () => {
    test('should handle supported platform with fallback', async () => {
      const options = matchDataToFetchOptions(mockTelegramMatch);
      const result = await fetchUserProfileData(options);
      
      // Should always succeed with at least fallback data
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.platform).toBe('Telegram');
      expect(result.data?.username).toBe('alice_crypto');
      expect(result.data?.profileId).toBe('123456789');
      
      // Source should be either 'ethos' or 'fallback'
      expect(['ethos', 'fallback']).toContain(result.source);
    });

    test('should handle unsupported platform with fallback', async () => {
      const options = matchDataToFetchOptions(mockUnsupportedMatch);
      const result = await fetchUserProfileData(options);
      
      // Should succeed with fallback data
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.source).toBe('fallback');
      expect(result.data?.platform).toBe('Discord');
      expect(result.data?.username).toBe('charlie_nft');
    });
  });

  describe('Error Handling', () => {
    test('should handle invalid options gracefully', async () => {
      const invalidOptions = {
        platform: '',
        realID: '',
        username: '',
        fallbackData: undefined
      };
      
      // This should still provide some fallback data
      const result = await fetchUserProfileData(invalidOptions);
      expect(result.data).toBeDefined();
    });
  });
});

// Manual testing helper (for development)
export const testUserProfileFlow = async () => {
  console.log('🧪 [TEST] Starting User Profile Flow Test...');
  
  const testCases = [mockTelegramMatch, mockFarcasterMatch, mockUnsupportedMatch];
  
  for (const match of testCases) {
    console.log(`\n📱 [TEST] Testing ${match.platform} user: ${match.matchedUsername}`);
    
    const options = matchDataToFetchOptions(match);
    const result = await fetchUserProfileData(options);
    
    console.log(`✅ [TEST] Result:`, {
      success: result.success,
      source: result.source,
      hasData: !!result.data,
      platform: result.data?.platform,
      username: result.data?.username,
      hasStats: !!result.data?.stats,
      error: result.error
    });
  }
  
  console.log('\n🎉 [TEST] User Profile Flow Test Complete!');
};
