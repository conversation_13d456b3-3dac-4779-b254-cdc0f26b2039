# === FRONTEND CONFIGURATION ===

# Next.js - Base URL for the application
# For development:
NEXT_PUBLIC_URL="http://localhost:3001"
# For production (Vercel):
# NEXT_PUBLIC_URL="https://your-app-name.vercel.app"

# NMLLAgent Backend URL - URL of the nmllagent backend service
NEXT_PUBLIC_NMLLAGENT_URL="http://localhost:3000"

# Note: Authentication is now handled via Farcaster Quick Auth
# No API tokens needed - authentication is automatic via Farcaster SDK

# === APP METADATA CONFIGURATION ===
# These variables control the app's appearance and branding

# App name displayed in Farcaster
NEXT_PUBLIC_APP_NAME="NoMoreLonelyLife"

# App description for metadata
NEXT_PUBLIC_APP_DESCRIPTION="AI-powered matching for meaningful connections"

# Button text for launching the app
NEXT_PUBLIC_APP_BUTTON_TEXT="Launch Template"

# App category in Farcaster directory
NEXT_PUBLIC_APP_PRIMARY_CATEGORY="developer-tools"

# Tags for app discovery (comma-separated)
NEXT_PUBLIC_APP_TAGS="monad,farcaster,miniapp,template"

# === FARCASTER INTEGRATION ===

# Neynar Client ID (REQUIRED for notifications)
# Get this from: https://neynar.com/
NEXT_PUBLIC_NEYNAR_CLIENT_ID="your-neynar-client-id-here"

# === DOMAIN VERIFICATION (OPTIONAL) ===
# Only needed if you want domain verification in Farcaster
# Generate using: https://farcaster.xyz/~/developers/mini-apps/manifest
# ACCOUNT_ASSOCIATION={"header":"...","payload":"...","signature":"..."}

# === LOCK SCREEN CONFIGURATION ===
# Static password for lock screen access (keep this secret)
LOCK_SCREEN_PASSWORD="your-secure-password-here"