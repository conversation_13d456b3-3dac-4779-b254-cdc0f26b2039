import { useEffect, useCallback } from 'react'
import { setupNotificationListener } from '@/lib/frame-notifications'

interface UseNotificationRefreshOptions {
  /**
   * Callback function to execute when a notification is received
   */
  onNotificationReceived?: () => void
  /**
   * Whether the refresh functionality is enabled
   */
  enabled?: boolean
  /**
   * Whether currently processing a message (to prevent conflicts)
   */
  isProcessing?: boolean
}

/**
 * Custom hook to handle automatic chat refresh when Farcaster notifications are received
 * 
 * This hook:
 * 1. Sets up a global notification listener
 * 2. Calls the provided callback when notifications are received
 * 3. Respects processing state to avoid conflicts
 * 4. Automatically cleans up listeners on unmount
 * 
 * @param options Configuration options
 * @returns Object with refresh trigger function
 */
export function useNotificationRefresh({
  onNotificationReceived,
  enabled = true,
  isProcessing = false
}: UseNotificationRefreshOptions) {
  
  // Memoized callback to prevent unnecessary re-renders
  const handleNotificationRefresh = useCallback((event: CustomEvent) => {
    console.log('📨 [NOTIFICATION HOOK] Received notification event:', event.detail)
    
    // Only trigger refresh if enabled and not currently processing
    if (enabled && !isProcessing && onNotificationReceived) {
      console.log('🔄 [NOTIFICATION HOOK] Triggering refresh callback...')
      onNotificationReceived()
    } else {
      console.log('⏸️ [NOTIFICATION HOOK] Refresh skipped:', {
        enabled,
        isProcessing,
        hasCallback: !!onNotificationReceived
      })
    }
  }, [enabled, isProcessing, onNotificationReceived])

  // Setup notification listener
  useEffect(() => {
    if (!enabled) return

    console.log('🎧 [NOTIFICATION HOOK] Setting up notification listener...')
    
    // Setup the global notification listener
    const cleanup = setupNotificationListener()
    
    // Add our specific handler
    if (typeof window !== 'undefined') {
      window.addEventListener('farcaster-notification-received', handleNotificationRefresh as EventListener)
    }

    return () => {
      console.log('🧹 [NOTIFICATION HOOK] Cleaning up notification listener...')
      cleanup?.()
      if (typeof window !== 'undefined') {
        window.removeEventListener('farcaster-notification-received', handleNotificationRefresh as EventListener)
      }
    }
  }, [enabled, handleNotificationRefresh])

  // Manual trigger function for external use
  const triggerRefresh = useCallback(() => {
    if (enabled && !isProcessing && onNotificationReceived) {
      console.log('🔄 [NOTIFICATION HOOK] Manual refresh triggered')
      onNotificationReceived()
    }
  }, [enabled, isProcessing, onNotificationReceived])

  return {
    triggerRefresh
  }
}
