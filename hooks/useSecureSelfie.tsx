import React, { useState, useEffect, useCallback } from 'react'
import { farcasterAuth } from '@/lib/farcaster-auth'

/**
 * 🔒 安全的Selfie显示Hook
 * 
 * 功能：
 * 1. 从后端获取临时签名URL
 * 2. 前端直接从Supabase加载图片
 * 3. 自动处理URL过期和重新获取
 * 4. 提供加载状态和错误处理
 */

interface SecureSelfieData {
  signedUrl: string
  expiresIn: number
  expiresAt: string
}

interface UseSecureSelfieOptions {
  realID: string
  platform: string
  requestorRealID?: string
  expires?: number // 自定义过期时间（秒）
  autoRefresh?: boolean // 是否在过期前自动刷新
}

interface UseSecureSelfieReturn {
  imageUrl: string | null
  isLoading: boolean
  error: string | null
  refresh: () => Promise<void>
  isExpired: boolean
}

export function useSecureSelfie({
  realID,
  platform,
  requestorRealID,
  expires = 3600, // 默认1小时
  autoRefresh = true
}: UseSecureSelfieOptions): UseSecureSelfieReturn {
  const [imageUrl, setImageUrl] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [expiresAt, setExpiresAt] = useState<Date | null>(null)
  const [refreshTimer, setRefreshTimer] = useState<NodeJS.Timeout | null>(null)

  // 检查URL是否过期
  const isExpired = expiresAt ? new Date() > expiresAt : false

  // 获取签名URL
  const fetchSignedUrl = useCallback(async (): Promise<void> => {
    if (!realID || !platform) return

    setIsLoading(true)
    setError(null)

    // 构建API URL (移到try外面，这样catch也能访问)
    const apiUrl = `/api/nmllagent/selfie`
    const queryParams = new URLSearchParams()

    // Add required parameters
    queryParams.append('realID', realID)
    queryParams.append('platform', platform)

    if (requestorRealID) {
      queryParams.append('requestorRealID', requestorRealID)
    }
    if (expires) {
      queryParams.append('expires', expires.toString())
    }

    const fullUrl = `${apiUrl}?${queryParams.toString()}`

    try {
      console.log('🚨🚨🚨 [SECURE SELFIE] Fetching signed URL 🚨🚨🚨', { realID, platform, requestorRealID })

      // 🔒 SECURITY: Use authenticated fetch for selfie requests
      const response = await farcasterAuth.authenticatedFetch(fullUrl)

      console.log('🚨🚨🚨 [SECURE SELFIE] Response received 🚨🚨🚨', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      })

      const data = await response.json()

      console.log('🚨🚨🚨 [SECURE SELFIE] Response data 🚨🚨🚨', {
        success: data.success,
        hasData: !!data.data,
        dataKeys: data.data ? Object.keys(data.data) : null,
        error: data.error
      })

      if (!response.ok) {
        throw new Error(data.error || 'Failed to get selfie')
      }

      const selfieData: SecureSelfieData = data.data

      console.log('🚨🚨🚨 [SECURE SELFIE] Setting state 🚨🚨🚨', {
        signedUrl: selfieData.signedUrl ? `${selfieData.signedUrl.substring(0, 50)}...` : 'null',
        signedUrlLength: selfieData.signedUrl ? selfieData.signedUrl.length : 0,
        expiresAt: selfieData.expiresAt,
        expiresIn: selfieData.expiresIn
      })

      setImageUrl(selfieData.signedUrl)
      setExpiresAt(new Date(selfieData.expiresAt))

      console.log('🚨🚨🚨 ✅ [SECURE SELFIE] State updated 🚨🚨🚨', {
        imageUrlSet: !!selfieData.signedUrl,
        expiresAtSet: !!selfieData.expiresAt
      })

      // 设置自动刷新定时器（在过期前5分钟刷新）
      if (autoRefresh && selfieData.expiresIn > 300) {
        const refreshTime = (selfieData.expiresIn - 300) * 1000 // 提前5分钟
        
        if (refreshTimer) {
          clearTimeout(refreshTimer)
        }

        const timer = setTimeout(() => {
          console.log('🔄 [SECURE SELFIE] Auto-refreshing expired URL')
          fetchSignedUrl()
        }, refreshTime)

        setRefreshTimer(timer)
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'

      // 🔍 Debug: Log more details for pattern matching errors
      if (errorMessage.includes('pattern')) {
        console.warn('🔍 [SECURE SELFIE] Pattern matching error (likely SDK internal):', {
          error: errorMessage,
          url: fullUrl,
          realID,
          platform
        })
      } else {
        console.error('❌ [SECURE SELFIE] Failed to fetch signed URL:', errorMessage)
      }

      // For pattern errors, use a more user-friendly message
      const userFriendlyMessage = errorMessage.includes('pattern')
        ? 'Selfie temporarily unavailable'
        : errorMessage

      setError(userFriendlyMessage)
      setImageUrl(null)
      setExpiresAt(null)
    } finally {
      setIsLoading(false)
    }
  }, [realID, platform, requestorRealID, expires, autoRefresh])

  // 手动刷新
  const refresh = useCallback(async (): Promise<void> => {
    await fetchSignedUrl()
  }, [fetchSignedUrl])

  // 初始加载
  useEffect(() => {
    fetchSignedUrl()

    // 清理定时器
    return () => {
      if (refreshTimer) {
        clearTimeout(refreshTimer)
      }
    }
  }, [fetchSignedUrl])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (refreshTimer) {
        clearTimeout(refreshTimer)
      }
    }
  }, [refreshTimer])

  return {
    imageUrl,
    isLoading,
    error,
    refresh,
    isExpired
  }
}

/**
 * 🖼️ 安全Selfie图片组件
 */
interface SecureSelfieImageProps extends UseSecureSelfieOptions {
  className?: string
  alt?: string
  onLoad?: () => void
  onError?: (error: string) => void
  fallback?: React.ReactNode
}

export function SecureSelfieImage({
  className = '',
  alt = 'User selfie',
  onLoad,
  onError,
  fallback,
  ...options
}: SecureSelfieImageProps) {
  const { imageUrl, isLoading, error, refresh, isExpired } = useSecureSelfie(options)

  useEffect(() => {
    if (error && onError) {
      onError(error)
    }
  }, [error, onError])

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center bg-gray-200 ${className}`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error || !imageUrl) {
    if (fallback) {
      return <>{fallback}</>
    }
    
    return (
      <div className={`flex flex-col items-center justify-center bg-gray-100 p-4 ${className}`}>
        <div className="text-gray-500 text-sm mb-2">
          {error || 'Selfie not available'}
        </div>
        <button 
          onClick={refresh}
          className="text-blue-600 text-sm hover:underline"
        >
          Try again
        </button>
      </div>
    )
  }

  return (
    <div className="relative">
      <img
        src={imageUrl}
        alt={alt}
        className={className}
        onLoad={onLoad}
        onError={() => onError?.('Failed to load image')}
      />
      {isExpired && (
        <div className="absolute top-2 right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded">
          Expired
        </div>
      )}
    </div>
  )
}
