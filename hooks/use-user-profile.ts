/**
 * React Hook for managing user profile data
 * Handles fetching, loading states, and error management
 */

import { useState, useCallback } from 'react';
import { 
  UserProfileData, 
  UserDataFetchOptions, 
  UseUserProfileReturn 
} from '@/types/user-profile';
import { 
  fetchUserProfileData, 
  validateFetchOptions 
} from '@/lib/user-data-service';

export function useUserProfile(): UseUserProfileReturn {
  const [profileData, setProfileData] = useState<UserProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUserProfile = useCallback(async (options: UserDataFetchOptions) => {
    // Validate options
    const validation = validateFetchOptions(options);
    if (!validation.isValid) {
      setError(`Invalid options: ${validation.errors.join(', ')}`);
      return;
    }

    setIsLoading(true);
    setError(null);
    setProfileData(null);

    try {
      const result = await fetchUserProfileData(options);
      
      if (result.success && result.data) {
        setProfileData(result.data);
        
        // Log source for debugging
        console.log(`📊 [HOOK] Profile loaded from ${result.source} for ${options.username}`);
      } else {
        // Even if not successful, we might have fallback data
        if (result.data) {
          setProfileData(result.data);
        }
        
        if (result.error) {
          setError(result.error);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch user profile';
      setError(errorMessage);
      console.error('❌ [HOOK] Profile fetch error:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const reset = useCallback(() => {
    setProfileData(null);
    setIsLoading(false);
    setError(null);
  }, []);

  return {
    profileData,
    isLoading,
    error,
    fetchUserProfile,
    clearError,
    reset
  };
}
