'use client'

import React, { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Eye, EyeOff, Lock, Unlock } from 'lucide-react'
import { cn } from '@/lib/utils'

interface LockScreenProps {
  onUnlock: () => void
}

interface UnlockResponse {
  success: boolean
  message?: string
}

export const LockScreen: React.FC<LockScreenProps> = ({ onUnlock }) => {
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [attempts, setAttempts] = useState(0)
  const inputRef = useRef<HTMLInputElement>(null)

  // Focus input on mount
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus()
    }
  }, [])

  // Static password verification (fallback)
  const verifyStaticPassword = (inputPassword: string): boolean => {
    // Use default password since LOCK_SCREEN_PASSWORD is server-side only
    const staticPassword = 'nomorelonely2024'
    return inputPassword === staticPassword
  }

  // API verification for invitation codes (future implementation)
  const verifyWithAPI = async (inputPassword: string): Promise<UnlockResponse> => {
    try {
      const response = await fetch('/api/verify-unlock', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          password: inputPassword,
          type: 'invitation_code' 
        }),
      })

      if (!response.ok) {
        throw new Error('API verification failed')
      }

      return await response.json()
    } catch (error) {
      console.warn('API verification unavailable, falling back to static password')
      return {
        success: verifyStaticPassword(inputPassword),
        message: verifyStaticPassword(inputPassword) ? 'Access granted' : 'Invalid password'
      }
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!password.trim()) {
      setError('Please enter a password')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      // Try API verification first, fallback to static password
      const result = await verifyWithAPI(password)
      
      if (result.success) {
        onUnlock()
      } else {
        setAttempts(prev => prev + 1)
        setError(result.message || 'Invalid password or invitation code')
        setPassword('')
        
        // Add delay after failed attempts
        if (attempts >= 2) {
          setTimeout(() => {
            if (inputRef.current) {
              inputRef.current.focus()
            }
          }, 1000)
        } else {
          if (inputRef.current) {
            inputRef.current.focus()
          }
        }
      }
    } catch (error) {
      setError('Verification failed. Please try again.')
      setPassword('')
      if (inputRef.current) {
        inputRef.current.focus()
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit(e as any)
    }
  }

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{ backgroundColor: "#141316" }}
    >
      {/* Background with subtle pattern matching AI interface */}
      <div
        className="absolute inset-0 opacity-5"
        style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
                           radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%)`,
        }}
      />

      <div className="relative w-full max-w-md mx-4">
        <div
          className="rounded-2xl p-8 shadow-2xl backdrop-blur-sm border border-zinc-800"
          style={{ backgroundColor: "#27252C" }}
        >
          {/* Header */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-zinc-700/30 rounded-full mb-4">
              <Lock className="w-8 h-8 text-gray-100" />
            </div>
            <h1 className="text-2xl font-bold text-gray-100 mb-2">
              NoMoreLonely.Life
            </h1>
            <p className="text-gray-400 text-sm">
              Enter your password or invitation code to continue
            </p>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <div className="relative">
                <Input
                  ref={inputRef}
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Password or invitation code"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  onKeyPress={handleKeyPress}
                  disabled={isLoading}
                  className={cn(
                    "pr-12 h-12 text-center text-lg font-medium bg-transparent border-zinc-600 text-gray-100 placeholder:text-gray-400 focus-visible:ring-zinc-500 focus-visible:border-zinc-500",
                    error && "border-red-500 focus-visible:ring-red-500"
                  )}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 text-gray-400 hover:text-gray-300 hover:bg-zinc-700"
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
              
              {error && (
                <p className="text-red-400 text-sm text-center animate-fade-in">
                  {error}
                </p>
              )}

              {attempts > 0 && (
                <p className="text-gray-500 text-xs text-center">
                  Attempts: {attempts}
                </p>
              )}
            </div>

            <Button
              type="submit"
              className={cn(
                "w-full h-12 text-lg font-medium rounded-full transition-all duration-200",
                password.trim() && !isLoading
                  ? "bg-white text-black hover:bg-gray-200"
                  : "bg-zinc-700 text-gray-400 cursor-not-allowed"
              )}
              disabled={isLoading || !password.trim()}
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  Verifying...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Unlock className="w-5 h-5" />
                  Unlock
                </div>
              )}
            </Button>
          </form>

          {/* Footer */}
          <div className="mt-8 text-center">
            <p className="text-xs text-gray-500">
              Secure access to NoMoreLonely.Life MinidApp
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
