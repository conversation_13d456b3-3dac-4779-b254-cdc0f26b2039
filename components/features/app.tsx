'use client'

import { useState, useEffect } from 'react'
import { Demo } from '@/components/features/home'
import { useFrame } from '@/components/providers/farcaster-provider'
import { SafeAreaContainer } from '@/components/layout/safe-area-container'
import ChatInterface from '@/components/features/chat/chat-interface'
import { NotificationTrigger } from '@/components/features/notifications/notification-trigger'
import { LockScreen } from '@/components/features/lock-screen/lock-screen'

export default function Home() {
  const { context, isLoading, isSDKLoaded } = useFrame()
  const [isUnlocked, setIsUnlocked] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // Handle client-side hydration and localStorage
  useEffect(() => {
    setIsClient(true)
    // Check if already unlocked from localStorage (only on client)
    if (typeof window !== 'undefined') {
      const unlocked = localStorage.getItem('nomorelonely_unlocked')
      if (unlocked === 'true') {
        setIsUnlocked(true)
      }
    }
  }, [])

  const handleUnlock = () => {
    setIsUnlocked(true)
    // Save to localStorage (only on client)
    if (typeof window !== 'undefined') {
      localStorage.setItem('nomorelonely_unlocked', 'true')
    }
  }

  // Show loading during hydration to prevent mismatch
  if (!isClient) {
    return (
      <div className="h-screen flex items-center justify-center" style={{ backgroundColor: "#141316" }}>
        <div className="text-gray-100">Loading...</div>
      </div>
    )
  }

  // Show lock screen if not unlocked
  if (!isUnlocked) {
    return <LockScreen onUnlock={handleUnlock} />
  }

  if (isLoading) {
    return (
      <SafeAreaContainer insets={context?.client.safeAreaInsets}>
        <div className="flex min-h-screen flex-col items-center justify-center p-4 space-y-8">
          <h1 className="text-3xl font-bold text-center">Loading...</h1>
        </div>
      </SafeAreaContainer>
    )
  }

  // For development/testing purposes, show ChatInterface even without SDK
  // In production, you might want to keep the SDK check
  if (!isSDKLoaded) {
    // Show ChatInterface for testing, but with a notice
    return (
      <div className="relative">
        <ChatInterface />
        <div className="fixed top-2 left-2 right-2 z-50 bg-yellow-100 border border-yellow-400 text-yellow-800 px-3 py-2 rounded text-sm">
          ⚠️ Development Mode: Farcaster SDK not detected. Some features may not work.
        </div>
      </div>
    )
  }

  // Use ChatInterface as the main interface, with original Demo available in sidebar
  return (
    <>
      <ChatInterface />
      <NotificationTrigger
        autoTrigger={true}
        triggerConditions={{
          onNewMessage: true,
          onUserActivity: false,
          onFocus: true
        }}
      />
    </>
  )
}
