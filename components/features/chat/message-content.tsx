"use client"

import React from 'react'
import { parseUserMentions, type MentionSegment } from '@/lib/utils'
import { useFrame } from '@/components/providers/farcaster-provider'

interface MessageContentProps {
  content: string
  isSystemMessage?: boolean
}

/**
 * Component to render message content with interactive user mentions
 * Handles Farcaster @fid clicks to open user profiles
 */
export const MessageContent: React.FC<MessageContentProps> = ({ 
  content, 
  isSystemMessage = false 
}) => {
  const { actions } = useFrame()

  // For user messages, render as plain text
  if (!isSystemMessage) {
    return <span style={{ whiteSpace: 'pre-wrap' }}>{content}</span>
  }

  // For system messages, parse mentions and render with interactive elements
  const segments = parseUserMentions(content)

  const handleFarcasterMentionClick = (fid: string) => {
    if (actions?.viewProfile) {
      try {
        const fidNumber = parseInt(fid, 10)
        if (!isNaN(fidNumber)) {
          actions.viewProfile({ fid: fidNumber })
        }
      } catch (error) {
        console.error('Failed to open profile for FID:', fid, error)
      }
    }
  }

  const renderMention = (segment: MentionSegment, index: number) => {
    const { realID, username, platform } = segment

    if (platform === 'telegram') {
      return `@${username} on telegram`
    } else if (platform === 'farcaster') {
      return (
        <span>
          <button
            onClick={() => handleFarcasterMentionClick(realID!)}
            className="text-blue-400 hover:text-blue-300 hover:underline cursor-pointer bg-transparent border-none p-0 font-inherit"
            style={{
              background: 'none',
              border: 'none',
              padding: 0,
              font: 'inherit',
              cursor: 'pointer',
              outline: 'inherit'
            }}
          >
            @{realID}
          </button>
          <span> - {username} on farcaster</span>
        </span>
      )
    } else {
      // Fallback for unknown platforms
      return `@${username} on ${platform}`
    }
  }

  return (
    <span style={{ whiteSpace: 'pre-wrap' }}>
      {segments.map((segment, index) => {
        if (segment.type === 'text') {
          return <span key={`text-${index}`}>{segment.content}</span>
        } else {
          return (
            <span key={`mention-${index}`}>
              {renderMention(segment, index)}
            </span>
          )
        }
      })}
    </span>
  )
}
