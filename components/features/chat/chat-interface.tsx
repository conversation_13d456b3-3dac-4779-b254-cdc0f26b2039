"use client"

import type React from "react"
import { useState, useR<PERSON>, useEffect, use<PERSON><PERSON>back } from "react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Camera } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { cn } from "@/lib/utils"
import { useFrame } from "@/components/providers/farcaster-provider"
import { nmllagentClient } from "@/lib/nmllagent-client"
import { useFarcasterAuth } from "@/lib/farcaster-auth"
import { MessageContent } from "@/components/features/chat/message-content"
import CameraInterface from "@/components/features/camera/CameraInterface"
import MatchesSidebar from "@/components/features/matches/matches-sidebar"
import { NotificationSetup } from "@/components/features/notifications/notification-setup"
import { useNotificationRefresh } from "@/hooks/use-notification-refresh"
import { useNotification } from "@/components/providers/notification-provider"



type ActiveButton = "none" | "add" | "deepSearch" | "think"
type MessageType = "user" | "system"

interface Message {
  id: string
  content: string
  type: MessageType
  completed?: boolean
  newSection?: boolean
  isTyping?: boolean
}

interface MessageSection {
  id: string
  messages: Message[]
  isNewSection: boolean
  isActive?: boolean
  sectionIndex: number
}

interface StreamingWord {
  id: number
  text: string
}

// Safe vibration function that works across devices
const triggerVibration = (duration = 50) => {
  if (typeof navigator !== "undefined" && "vibrate" in navigator) {
    navigator.vibrate(duration)
  }
}

// Optimized word-level streaming for better visual effect
const WORD_DELAY = 60 // ms per word - balanced speed for better readability
const CHUNK_SIZE = 2 // Number of words to add at once



const ChatInterface: React.FC = () => {
  const { context } = useFrame()
  const { } = useNotification()
  const {
    isAuthenticated,
    isLoading: isAuthLoading,
    initialize: initializeAuth,
    authenticatedFetch
  } = useFarcasterAuth()
  const [inputValue, setInputValue] = useState("")
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const chatContainerRef = useRef<HTMLDivElement>(null)
  const newSectionRef = useRef<HTMLDivElement>(null)
  const [hasTyped, setHasTyped] = useState(false)
  const [activeButton, setActiveButton] = useState<ActiveButton>("none")
  const [isMobile, setIsMobile] = useState<boolean | undefined>(undefined) // 避免hydration错误
  const [messages, setMessages] = useState<Message[]>([])
  const [messageSections, setMessageSections] = useState<MessageSection[]>([])
  const [isStreaming, setIsStreaming] = useState(false)
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)
  const [isProcessingMessage, setIsProcessingMessage] = useState(false) // Track if we're processing a user message
  const [streamingWords, setStreamingWords] = useState<StreamingWord[]>([])
  const [streamingMessageId, setStreamingMessageId] = useState<string | null>(null)
  const [completedMessages, setCompletedMessages] = useState<Set<string>>(new Set())
  const [viewportHeight, setViewportHeight] = useState<number | undefined>(undefined) // 避免hydration错误
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [activeSectionId, setActiveSectionId] = useState<string | null>(null)
  const [shouldTriggerNotificationSetup, setShouldTriggerNotificationSetup] = useState(false)
  const [hasTriggeredNotificationSetup, setHasTriggeredNotificationSetup] = useState(false)
  const inputContainerRef = useRef<HTMLDivElement>(null)
  const shouldFocusAfterStreamingRef = useRef(false)
  const mainContainerRef = useRef<HTMLDivElement>(null)
  const selectionStateRef = useRef<{ start: number | null; end: number | null }>({ start: null, end: null })
  const [isCameraOpen, setIsCameraOpen] = useState(false)
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [selfieError, setSelfieError] = useState<string | null>(null)
  const [selfieSuccess, setSelfieSuccess] = useState(false)

  // Simple ID generator for UI sections (not for messages - those come from backend)
  const generateId = useCallback((prefix: string) => {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
  }, [])



  // Load chat history function (extracted for reuse)
  const loadChatHistory = async (silent = false, preserveTemp = false) => {
    if (!context?.user?.fid) return

    if (!silent) setIsLoadingHistory(true)
    try {
      // 🔒 SECURITY: FID now extracted from JWT token on backend
      const response = await nmllagentClient.getHistory()

      if (response.success && response.history.length > 0) {
        // Convert history to frontend message format
        const historyMessages: Message[] = response.history.map((msg, index) => ({
          id: msg.id,
          content: msg.content,
          type: msg.type,
          completed: true,
          // Create new section for each message to ensure they display separately
          // This is especially important for notification messages
          newSection: index === 0 || msg.type === 'system'
        }))

        if (preserveTemp) {
          // Preserve temp messages and merge intelligently
          setMessages(currentMessages => {
            const tempMessages = currentMessages.filter(msg =>
              msg.id.includes('temp') || msg.id.includes('thinking')
            )

            // Start with history messages
            let mergedMessages = [...historyMessages]

            // Add temp messages that don't have real counterparts
            tempMessages.forEach(tempMsg => {
              const realMatch = historyMessages.find(realMsg =>
                realMsg.content === tempMsg.content && realMsg.type === tempMsg.type
              )
              if (!realMatch) {
                mergedMessages.push(tempMsg)
              }
            })

            return mergedMessages
          })
        } else {
          // Replace all messages, but handle temp user messages specially
          setMessages(currentMessages => {
            const tempUserMessages = currentMessages.filter(msg =>
              msg.id.includes('user-temp')
            )

            // Start with history messages
            let finalMessages = [...historyMessages]

            // For each temp user message, check if it's in history
            tempUserMessages.forEach(tempMsg => {
              const realMatch = historyMessages.find(realMsg =>
                realMsg.content === tempMsg.content && realMsg.type === 'user'
              )
              if (!realMatch) {
                // Keep temp message if not found in history yet
                finalMessages.push(tempMsg)
              }
            })

            return finalMessages
          })
        }
      }
    } catch (error) {
      console.error('Failed to load chat history:', error)
    } finally {
      if (!silent) setIsLoadingHistory(false)
    }
  }

  // Optimized function to check for new messages and append only new ones
  const checkForNewMessages = useCallback(async () => {
    if (!context?.user?.fid) return

    try {
      // 🔒 SECURITY: FID now extracted from JWT token on backend
      const response = await nmllagentClient.getHistory()

      if (response.success && response.history.length > 0) {
        // Use callback to get current messages state
        setMessages(currentMessages => {
          // Get current message IDs for comparison (excluding temp messages)
          const currentMessageIds = new Set(
            currentMessages
              .filter(msg => !msg.id.includes('temp') && !msg.id.includes('thinking'))
              .map(msg => msg.id)
          )

          // Find new messages that don't exist in current messages
          const newMessages = response.history.filter(msg => !currentMessageIds.has(msg.id))

          if (newMessages.length > 0) {
            console.log(`🔔 [POLLING] Detected ${newMessages.length} new message(s)`)

            // Convert new messages to frontend format
            const newFrontendMessages: Message[] = newMessages.map((msg) => ({
              id: msg.id,
              content: msg.content,
              type: msg.type,
              completed: true,
              newSection: msg.type === 'system' // System messages (notifications) create new sections
            }))

            // Trigger vibration for new messages (typically notifications)
            if (newFrontendMessages.some(msg => msg.type === 'system')) {
              triggerVibration(100)
            }

            // Intelligently merge messages - replace temp messages with real ones
            let updatedMessages = [...currentMessages]

            // Replace temp user messages with real ones if found
            newMessages.forEach(newMsg => {
              if (newMsg.type === 'user') {
                const tempIndex = updatedMessages.findIndex(msg =>
                  msg.id.includes('temp') &&
                  msg.type === 'user' &&
                  msg.content === newMsg.content
                )
                if (tempIndex !== -1) {
                  updatedMessages[tempIndex] = {
                    id: newMsg.id,
                    content: newMsg.content,
                    type: newMsg.type,
                    completed: true,
                    newSection: updatedMessages[tempIndex].newSection
                  }
                  return
                }
              }
            })

            // Add truly new messages that aren't replacements
            const messagesToAdd = newFrontendMessages.filter(newMsg => {
              return !updatedMessages.some(existing =>
                existing.content === newMsg.content && existing.type === newMsg.type
              )
            })

            return [...updatedMessages, ...messagesToAdd]
          }

          // No new messages, return current state unchanged
          return currentMessages
        })
      }
    } catch (error) {
      console.error('Failed to check for new messages:', error)
    }
  }, [context?.user?.fid]) // Only depend on fid, not messages

  // Enhanced version of checkForNewMessages with streaming animation support
  const checkForNewMessagesWithStreaming = useCallback(async () => {
    if (!context?.user?.fid) return

    try {
      // Get current messages snapshot
      const currentMessagesSnapshot = await new Promise<Message[]>((resolve) => {
        setMessages((msgs) => {
          resolve(msgs)
          return msgs
        })
      })

      // Check for new messages
      await checkForNewMessages()

      // After a brief delay, check if we got new AI messages and trigger streaming
      setTimeout(async () => {
        const updatedMessages = await new Promise<Message[]>((resolve) => {
          setMessages((msgs) => {
            resolve(msgs)
            return msgs
          })
        })

        // Find new AI messages that weren't in the previous snapshot
        const newAIMessages = updatedMessages.filter(msg =>
          msg.type === 'system' &&
          !currentMessagesSnapshot.some(oldMsg => oldMsg.id === msg.id) &&
          !msg.id.includes('temp') &&
          !msg.id.includes('thinking') &&
          msg.completed // Only trigger streaming for completed messages
        )

        // If we found new AI messages, trigger streaming animation for the latest one
        if (newAIMessages.length > 0) {
          const latestAIMessage = newAIMessages[newAIMessages.length - 1]
          console.log('🎬 [NEW MESSAGE STREAMING] Found new AI message, starting streaming animation')

          if (latestAIMessage.content && !completedMessages.has(latestAIMessage.id)) {
            // Mark as incomplete first to trigger streaming
            setMessages(prev => prev.map(msg =>
              msg.id === latestAIMessage.id
                ? { ...msg, completed: false, content: '' }
                : msg
            ))

            // Start streaming animation
            await simulateTextStreaming(latestAIMessage.content, latestAIMessage.id)
          }
        }
      }, 100)
    } catch (error) {
      console.error('Failed to check for new messages with streaming:', error)
    }
  }, [context?.user?.fid, checkForNewMessages, completedMessages])

  // Initialize authentication when context is available
  useEffect(() => {
    if (context?.user?.fid && !isAuthenticated && !isAuthLoading) {
      console.log('🔒 [CHAT] Initializing authentication for FID:', context.user.fid)
      initializeAuth().catch(error => {
        console.error('🔒 [CHAT] Authentication initialization failed:', error)
      })
    }
  }, [context?.user?.fid, isAuthenticated, isAuthLoading, initializeAuth])

  // Load chat history when component mounts and user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      loadChatHistory()
    }
  }, [context?.user?.fid, isAuthenticated])

  // Setup notification refresh using custom hook
  useNotificationRefresh({
    onNotificationReceived: () => {
      console.log('📨 [CHAT] Notification received, checking for new messages...')
      if (context?.user?.fid && !isProcessingMessage) {
        checkForNewMessagesWithStreaming() // Use enhanced version with streaming animation
      }
    },
    enabled: !!context?.user?.fid,
    isProcessing: isProcessingMessage
  })

  // Polling for new messages (notifications) - With streaming animation support
  useEffect(() => {
    if (!context?.user?.fid) return

    // Poll every 15 seconds for new messages
    const pollInterval = setInterval(async () => {
      // Only poll if not currently streaming AND not processing a message (to avoid race conditions)
      if (!isStreaming && !isProcessingMessage) {
        console.log('🔄 [POLLING] Checking for new messages with streaming animation...')
        await checkForNewMessagesWithStreaming() // Use streaming version for AI message animations
      }
    }, 15000) // 15 seconds

    return () => clearInterval(pollInterval)
  }, [context?.user?.fid, isStreaming, isProcessingMessage, checkForNewMessagesWithStreaming]) // Use streaming version

  // Check for new messages when page regains focus - Optimized to prevent flicker and race conditions
  useEffect(() => {
    const handleFocus = () => {
      if (!isStreaming && !isProcessingMessage && context?.user?.fid) {
        console.log('🔍 [FOCUS] Page regained focus, checking for new messages...')
        checkForNewMessagesWithStreaming() // Use enhanced version with streaming animation
      }
    }

    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [context?.user?.fid, isStreaming, isProcessingMessage, checkForNewMessagesWithStreaming]) // Add isProcessingMessage to prevent race conditions

  // Check if device is mobile and get viewport height
  useEffect(() => {
    const checkMobileAndViewport = () => {
      const isMobileDevice = window.innerWidth < 768
      setIsMobile(isMobileDevice)

      // Use visualViewport API for more accurate viewport height (considering virtual keyboard)
      const vh = window.visualViewport?.height || window.innerHeight
      setViewportHeight(vh)

      // Set CSS custom property for responsive design
      document.documentElement.style.setProperty("--vh", `${vh * 0.01}px`)

      // Apply fixed height to main container on mobile
      if (isMobileDevice && mainContainerRef.current) {
        mainContainerRef.current.style.height = `${vh}px`
      }
    }

    checkMobileAndViewport()

    // Listen for viewport changes (including virtual keyboard show/hide)
    const handleViewportChange = () => {
      checkMobileAndViewport()

      // 当键盘弹出/收起时，确保当前内容可见
      if (isMobile && chatContainerRef.current) {
        setTimeout(() => {
          const scrollContainer = chatContainerRef.current
          if (scrollContainer && messages.length > 0) {
            const lastMessage = messages[messages.length - 1]
            if (lastMessage.type === "system") {
              // 重新调整AI回复的可见性
              const containerHeight = scrollContainer.clientHeight
              const inputHeight = 120 // 移动端输入框高度
              const safeScrollPosition = scrollContainer.scrollHeight - containerHeight + inputHeight + 20

              scrollContainer.scrollTo({
                top: Math.max(0, safeScrollPosition),
                behavior: "smooth",
              })
            }
          }
        }, 100)
      }
    }

    // Listen for screen orientation changes
    const handleOrientationChange = () => {
      setTimeout(checkMobileAndViewport, 100) // Delay execution to ensure correct dimensions
    }

    window.addEventListener("resize", handleViewportChange)
    window.addEventListener("orientationchange", handleOrientationChange)

    // Listen for visualViewport changes (modern browsers)
    if (window.visualViewport) {
      window.visualViewport.addEventListener("resize", handleViewportChange)
    }

    return () => {
      window.removeEventListener("resize", handleViewportChange)
      window.removeEventListener("orientationchange", handleOrientationChange)
      if (window.visualViewport) {
        window.visualViewport.removeEventListener("resize", handleViewportChange)
      }
    }
  }, [])

  // Organize messages into sections
  useEffect(() => {
    if (messages.length === 0) {
      setMessageSections([])
      setActiveSectionId(null)
      return
    }

    const sections: MessageSection[] = []
    let currentSection: MessageSection = {
      id: generateId("section"),
      messages: [],
      isNewSection: false,
      sectionIndex: 0,
    }

    messages.forEach((message) => {
      if (message.newSection) {
        // Start a new section
        if (currentSection.messages.length > 0) {
          // Mark previous section as inactive
          sections.push({
            ...currentSection,
            isActive: false,
          })
        }

        // Create new active section
        const newSectionId = generateId("section")
        currentSection = {
          id: newSectionId,
          messages: [message],
          isNewSection: true,
          isActive: true,
          sectionIndex: sections.length,
        }

        // Update active section ID
        setActiveSectionId(newSectionId)
      } else {
        // Add to current section
        currentSection.messages.push(message)
      }
    })

    // Add the last section if it has messages
    if (currentSection.messages.length > 0) {
      sections.push(currentSection)
    }

    setMessageSections(sections)
  }, [messages])

  // AI's last reply always scrolls to the middle of the screen
  useEffect(() => {
    if (messages.length > 0) {
      setTimeout(() => {
        const scrollContainer = chatContainerRef.current
        if (scrollContainer) {
          // Get the last message
          const lastMessage = messages[messages.length - 1]

          if (lastMessage.type === "system") {
            // AI回复：确保内容不被输入框遮挡，滚动到合适位置
            const containerHeight = scrollContainer.clientHeight
            const inputHeight = isMobile ? 120 : 100 // 估算输入框高度
            const safeScrollPosition = scrollContainer.scrollHeight - containerHeight + inputHeight + 20 // 额外20px缓冲

            scrollContainer.scrollTo({
              top: Math.max(0, safeScrollPosition),
              behavior: "smooth",
            })
          } else {
            // 用户消息：滚动到底部
            scrollContainer.scrollTo({
              top: scrollContainer.scrollHeight,
              behavior: "smooth",
            })
          }
        }
      }, 100)
    }
  }, [messages])

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current
    if (textarea) {
      // 重置高度以获取正确的scrollHeight
      textarea.style.height = "auto"
      // 设置新高度，但确保不小于最小高度
      const minHeight = isMobile ? 48 : 44
      const newHeight = Math.max(textarea.scrollHeight, minHeight)
      textarea.style.height = `${newHeight}px`
    }
  }, [inputValue, isMobile])

  const focusTextarea = () => {
    if (textareaRef.current && !isMobile) {
      textareaRef.current.focus()
    }
  }

  // Focus textarea when not streaming
  useEffect(() => {
    if (!isStreaming && shouldFocusAfterStreamingRef.current) {
      focusTextarea()
      shouldFocusAfterStreamingRef.current = false
    }
  }, [isStreaming, isMobile])

  const simulateTextStreaming = async (text: string, messageId: string) => {
    // Split text into words for fast, smooth animation
    const words = text.split(" ")
    let currentIndex = 0
    setStreamingWords([])
    setIsStreaming(true)
    setStreamingMessageId(messageId)

    return new Promise<void>((resolve) => {
      const streamInterval = setInterval(() => {
        if (currentIndex < words.length) {
          // Add words in chunks for smooth animation
          const wordsToAdd = Math.min(CHUNK_SIZE, words.length - currentIndex)

          for (let i = 0; i < wordsToAdd; i++) {
            const wordIndex = currentIndex + i
            if (wordIndex < words.length) {
              setStreamingWords((prev) => [
                ...prev,
                {
                  id: wordIndex,
                  text: words[wordIndex] + " ",
                },
              ])
            }
          }

          currentIndex += wordsToAdd
        } else {
          clearInterval(streamInterval)

          // Update the message with final content
          setMessages((prev) => prev.map(msg =>
            msg.id === messageId
              ? { ...msg, content: text, completed: true }
              : msg
          ))

          setIsStreaming(false)
          setStreamingWords([])
          setStreamingMessageId(null)

          // Mark message as completed to prevent re-animation
          setCompletedMessages((prev) => new Set(prev).add(messageId))

          resolve()
        }
      }, WORD_DELAY)
    })
  }

  

  const simulateAIResponse = async (userMessage: string) => {
    // Check if user is logged in
    if (!context?.user?.fid) {
      console.error('User not logged in')
      return
    }

    // Set processing state to prevent polling interference
    setIsProcessingMessage(true)
    console.log('🔒 [AI RESPONSE] Message processing started - polling paused')

    // Add a temporary thinking message with typing indicator
    const tempThinkingId = `thinking-${Date.now()}`
    setMessages((prev) => [
      ...prev,
      {
        id: tempThinkingId,
        content: "",
        type: "system",
        isTyping: true,
        newSection: true,
      },
    ])

    try {
      // Call real nmllagent API
      const response = await nmllagentClient.sendMessage({
        text: userMessage,
        fid: context.user.fid.toString(),
        username: context.user.username || context.user.fid.toString()
      })

      if (response.success) {
        console.log('✅ [AI RESPONSE] Message sent successfully')

        // Wait for AI response ( 4s for better AI response timing)
        await new Promise((resolve) => setTimeout(resolve, 4000))

        // Check for new messages with streaming animation (unified approach)
        await checkForNewMessagesWithStreaming()

        // Only remove thinking message after streaming animation is set up
        setTimeout(() => {
          setMessages((prev) => prev.filter(msg => msg.id !== tempThinkingId))
        }, 200) // Small delay to ensure streaming animation starts first

      } else {
        console.error('❌ [AI RESPONSE] Backend returned error')
        const errorMessage = "Sorry, our LLM is being stupid. Please try again later."
        const errorMessageId = `error-${Date.now()}`

        // Remove thinking message and add error message
        setMessages((prev) => prev.filter(msg => msg.id !== tempThinkingId))
        await new Promise((resolve) => setTimeout(resolve, 4000))

        setMessages((prev) => [
          ...prev,
          {
            id: errorMessageId,
            content: "",
            type: "system",
            newSection: true,
          },
        ])

        await new Promise((resolve) => setTimeout(resolve, 300))
        await simulateTextStreaming(errorMessage, errorMessageId)
      }

    } catch (error) {
      console.error('❌ [AI RESPONSE] Network error:', error)
      const errorMessage = "Sorry, I'm having trouble connecting right now. Please try again."
      const errorMessageId = `error-${Date.now()}`

      // Remove thinking message and add error message
      setMessages((prev) => prev.filter(msg => msg.id !== tempThinkingId))
      await new Promise((resolve) => setTimeout(resolve, 4000))

      setMessages((prev) => [
        ...prev,
        {
          id: errorMessageId,
          content: "",
          type: "system",
          newSection: true,
        },
      ])

      await new Promise((resolve) => setTimeout(resolve, 300))
      await simulateTextStreaming(errorMessage, errorMessageId)
    }

    // Backup check with streaming animation support
    setTimeout(async () => {
      if (isProcessingMessage) {
        console.log('🔄 [AI RESPONSE] Backup refresh check...')

        // Get current messages before checking for new ones
        const currentMessagesSnapshot = await new Promise<Message[]>((resolve) => {
          setMessages((msgs) => {
            resolve(msgs)
            return msgs
          })
        })

        await checkForNewMessages()

        // Check if we got new AI messages and trigger streaming animation
        setTimeout(async () => {
          const updatedMessages = await new Promise<Message[]>((resolve) => {
            setMessages((msgs) => {
              resolve(msgs)
              return msgs
            })
          })

          // Find new AI messages that weren't in the previous snapshot
          const newAIMessages = updatedMessages.filter(msg =>
            msg.type === 'system' &&
            !currentMessagesSnapshot.some(oldMsg => oldMsg.id === msg.id) &&
            !msg.id.includes('temp') &&
            !msg.id.includes('thinking')
          )

          // If we found new AI messages, trigger streaming animation for the latest one
          if (newAIMessages.length > 0) {
            const latestAIMessage = newAIMessages[newAIMessages.length - 1]
            console.log('🎬 [BACKUP CHECK] Found new AI message, starting streaming animation')

            // Remove thinking message now that we have the AI response
            setMessages((prev) => prev.filter(msg => msg.id !== tempThinkingId))

            if (latestAIMessage.content) {
              await simulateTextStreaming(latestAIMessage.content, latestAIMessage.id)
            }
          }
        }, 100)
      }
    }, 9500) // Backup check after 9.5 seconds

    // No need for final refresh since we already loaded from backend

    // 在第一次 AI 回复完成后触发通知设置
    if (!hasTriggeredNotificationSetup) {
      console.log('🔔 [NOTIFICATION] Triggering notification setup after first AI response')
      setShouldTriggerNotificationSetup(true)
      setHasTriggeredNotificationSetup(true)
    }

    // Add vibration when complete
    triggerVibration(50)

    // Reset streaming state
    setIsStreaming(false)
    shouldFocusAfterStreamingRef.current = true

    // Clear processing state to resume polling
    setIsProcessingMessage(false)
    console.log('🔓 [AI RESPONSE] Message processing completed - polling resumed')

    // 确保AI回复完成后内容不被输入框遮挡
    setTimeout(() => {
      const scrollContainer = chatContainerRef.current
      if (scrollContainer) {
        // 确保AI回复底部在输入框上方
        const containerHeight = scrollContainer.clientHeight
        const inputHeight = isMobile ? 120 : 100 // 估算输入框高度
        const safeScrollPosition = scrollContainer.scrollHeight - containerHeight + inputHeight + 20 // 额外20px缓冲

        scrollContainer.scrollTo({
          top: Math.max(0, safeScrollPosition),
          behavior: "smooth",
        })
      }
    }, 200)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (inputValue.trim() && !isStreaming) {
      // Add vibration when message is submitted
      triggerVibration(50)

      const userMessage = inputValue.trim()

      // Add user message immediately for instant feedback
      const tempUserMessageId = `user-temp-${Date.now()}-${userMessage.slice(0, 10)}`
      const newUserMessage = {
        id: tempUserMessageId,
        content: userMessage,
        type: "user" as MessageType,
        newSection: messages.length > 0,
      }

      // Add user message immediately
      setMessages((prev) => [...prev, newUserMessage])

      // Reset input before starting the AI response
      setInputValue("")
      setHasTyped(false)
      setActiveButton("none")

      if (textareaRef.current) {
        textareaRef.current.style.height = "auto"
      }

      // Only focus the textarea on desktop, not on mobile
      if (!isMobile) {
        focusTextarea()
      } else {
        // On mobile, blur the textarea to dismiss the keyboard
        if (textareaRef.current) {
          textareaRef.current.blur()
        }
      }

      // Start AI response
      simulateAIResponse(userMessage)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Handle Cmd+Enter on both mobile and desktop
    if (!isStreaming && e.key === "Enter" && e.metaKey) {
      e.preventDefault()
      handleSubmit(e)
      return
    }

    // Only handle regular Enter key (without Shift) on desktop
    if (!isStreaming && !isMobile && e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSubmit(e)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value
    setInputValue(value)
    setHasTyped(value.length > 0)
  }

  const handleInputContainerClick = () => {
    if (!isMobile) {
      focusTextarea()
    }
  }

  const handleClearChat = () => {
    // Placeholder for clear chat functionality
    triggerVibration(50)
  }

  const handleAddSelfie = () => {
    // Open camera interface
    triggerVibration(50)
    setIsCameraOpen(true)
  }

  const handleCameraClose = () => {
    setIsCameraOpen(false)
  }

  const handlePhotoCapture = async (photoBlob: Blob) => {
    console.log('Photo captured for nmllagent integration:', photoBlob)

    try {
      // Get user context from Farcaster
      if (!context?.user?.fid) {
        console.error('No Farcaster user context available')
        // Show error message
        setSelfieError('Please log in to Farcaster first')
        setTimeout(() => setSelfieError(null), 3000)
        return
      }

      const fid = context.user.fid.toString()
      const username = context.user.username || context.user.displayName || fid

      // Create FormData for direct file upload (simplest approach)
      const formData = new FormData()
      formData.append('file', photoBlob, 'selfie.webp')
      formData.append('realID', fid)
      formData.append('platform', 'farcaster')
      formData.append('username', username)

      // Generate entityId and roomId using same logic as nmllagent backend
      // This should match the createUniqueUuid(runtime, fid) logic
      const entityId = `entity-${fid}-farcaster`
      const roomId = `room-${fid}-farcaster`
      formData.append('entityId', entityId)
      formData.append('roomId', roomId)

      console.log('🚀 [SELFIE UPLOAD] Starting upload...', { fid, username, entityId })
      console.log('🚀 [SELFIE UPLOAD] FormData entries:')
      for (const [key, value] of formData.entries()) {
        if (value instanceof File) {
          console.log(`  ${key}: File(${value.name}, ${value.size} bytes, ${value.type})`)
        } else {
          console.log(`  ${key}: ${value}`)
        }
      }

      // Upload to nmllagent backend using authenticated fetch with FormData
      const response = await authenticatedFetch('/api/nmllagent/selfie/upload', {
        method: 'POST',
        body: formData
        // Don't set Content-Type - let browser handle it for FormData
        // Authorization header will be added automatically by authenticatedFetch
      })

      console.log('📊 [SELFIE UPLOAD] Response status:', response.status, response.statusText)

      const result = await response.json()

      if (response.ok && result.success) {
        console.log('Selfie uploaded successfully:', result.data)

        // Show success message
        setSelfieSuccess(true)
        setTimeout(() => {
          setSelfieSuccess(false)
          setIsCameraOpen(false) // Close camera after successful upload
        }, 2000)

      } else {
        console.error('Selfie upload failed:', result.error)

        // Check if user needs to chat first
        if (result.error?.includes('Please chat with NoMoreLonely.Life Agent first')) {
          // Show specific error message
          setSelfieError('Please chat with NoMoreLonely.Life Agent first')
          setTimeout(() => setSelfieError(null), 3000)
        } else {
          // Show generic error
          setSelfieError('❌ 上传失败！请重试 ❌')
          setTimeout(() => setSelfieError(null), 3000)
        }
      }

    } catch (error) {
      console.error('Error uploading selfie:', error)
      setSelfieError('Network error. Please check your connection.')
      setTimeout(() => setSelfieError(null), 3000)
    }
  }

  const handleSidebarToggle = () => {
    setIsSidebarOpen(!isSidebarOpen)
    triggerVibration(50)
  }

  const renderMessage = (message: Message) => {

    return (
      <div key={message.id} className={cn("flex flex-col", message.type === "user" ? "items-end" : "items-start")}>
        <div
          className={cn(
            "px-4 py-3 rounded-2xl font-normal break-words",
            isMobile ? "max-w-[85%] text-sm" : "max-w-[80%] text-sm",
            message.type === "user" ? "text-gray-100" : "text-gray-100",
          )}
          style={message.type === "user" ? { backgroundColor: "#27252C" } : {}}
        >
          {/* Show typing indicator */}
          {message.isTyping && (
            <div className="flex items-center space-x-1">
              <div className="flex space-x-1">
                <div
                  className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: "0ms" }}
                ></div>
                <div
                  className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: "150ms" }}
                ></div>
                <div
                  className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce"
                  style={{ animationDelay: "300ms" }}
                ></div>
              </div>
              <span className="text-gray-400 text-xs ml-2 font-normal">Hmmm...let me think...</span>
            </div>
          )}

          {/* For user messages or completed system messages, render without animation */}
          {message.content && !message.isTyping && message.id !== streamingMessageId && (
            <span className={message.type === "system" && !completedMessages.has(message.id) ? "animate-fade-in" : ""}>
              <MessageContent
                content={message.content}
                isSystemMessage={message.type === "system"}
              />
            </span>
          )}

          {/* For streaming messages, render with word-level animation */}
          {message.id === streamingMessageId && !message.isTyping && (
            <span className="inline" style={{ whiteSpace: 'pre-wrap' }}>
              {streamingWords.map((word) => (
                <span key={word.id} className="animate-fade-in inline">
                  {word.text}
                </span>
              ))}
            </span>
          )}
        </div>
      </div>
    )
  }

  // 避免hydration错误，等待客户端初始化完成
  if (isMobile === undefined || viewportHeight === undefined) {
    return (
      <div className="h-screen bg-background flex items-center justify-center">
        <div className="text-foreground">Loading...</div>
      </div>
    )
  }

  // Show loading state while authenticating
  if (isAuthLoading) {
    return (
      <div className="h-screen bg-background flex items-center justify-center">
        <div className="text-foreground">Authenticating...</div>
      </div>
    )
  }

  // Show loading state while loading chat history
  if (isLoadingHistory) {
    return (
      <div className="h-screen bg-background flex items-center justify-center">
        <div className="text-foreground">Loading chat history...</div>
      </div>
    )
  }

  // Show camera interface if open
  if (isCameraOpen) {
    return (
      <CameraInterface
        onClose={handleCameraClose}
        onPhotoCapture={handlePhotoCapture}
      />
    )
  }

  return (
    <div
      ref={mainContainerRef}
      className="flex flex-col overflow-hidden font-medium relative"
      style={{
        height: isMobile ? `${viewportHeight}px` : "100svh",
        backgroundColor: "#141316",
        minHeight: isMobile ? `${viewportHeight}px` : "100svh",
        maxHeight: isMobile ? `${viewportHeight}px` : "100svh",
      }}
    >
      <header
        className={cn(
          "fixed top-0 left-0 right-0 flex items-center px-3 z-20 safe-area-top",
          isMobile ? "h-14" : "h-12",
        )}
        style={{
          backgroundColor: "#141316",
          paddingTop: isMobile ? "env(safe-area-inset-top)" : "0",
        }}
      >
        <div className="w-full flex items-center justify-between px-1">
          <Button
            variant="ghost"
            size="icon"
            className={cn("rounded-full hover:bg-zinc-800", isMobile ? "h-10 w-10" : "h-8 w-8")}
          >
            <Menu className={cn("text-gray-100", isMobile ? "h-6 w-6" : "h-5 w-5")} />
            <span className="sr-only">Menu</span>
          </Button>

          <h1 className={cn("font-medium text-gray-100 truncate px-2", isMobile ? "text-lg" : "text-base")}>
            NoMoreLonely.Life
          </h1>

          {/* Sidebar toggle button */}
          <Button
            variant="ghost"
            size="icon"
            className={cn("rounded-full hover:bg-zinc-800", isMobile ? "h-10 w-10" : "h-8 w-8")}
            onClick={handleSidebarToggle}
          >
            <Users className={cn("text-gray-100", isMobile ? "h-6 w-6" : "h-5 w-5")} />
            <span className="sr-only">Contacts</span>
          </Button>
        </div>
      </header>

      <div
        ref={chatContainerRef}
        className={cn(
          "flex-grow overflow-y-auto overflow-x-hidden scroll-smooth",
          isMobile ? "pt-16 px-3 mobile-chat-container" : "pt-12 px-4",
        )}
        style={{
          // 使用固定的大padding确保内容不被遮挡
          paddingBottom: isMobile ? "280px" : "160px",
        }}
      >
        <div className={cn("mx-auto space-y-4", isMobile ? "max-w-full" : "max-w-3xl")}>
          {messageSections.map((section, sectionIndex) => (
            <div
              key={section.id}
              ref={sectionIndex === messageSections.length - 1 && section.isNewSection ? newSectionRef : null}
            >
              {section.isNewSection && (
                <div className="pt-4 flex flex-col justify-start">
                  {section.messages.map((message) => renderMessage(message))}
                </div>
              )}

              {!section.isNewSection && <div>{section.messages.map((message) => renderMessage(message))}</div>}
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Selfie Success/Error Messages */}
      {(selfieSuccess || selfieError) && (
        <div className="fixed top-20 left-1/2 transform -translate-x-1/2 z-30">
          <div className={cn(
            "px-4 py-2 rounded-lg text-white text-sm font-medium shadow-lg",
            selfieSuccess ? "bg-green-600" : "bg-red-600"
          )}>
            {selfieSuccess ? "Selfie uploaded successfully!" : selfieError}
          </div>
        </div>
      )}

      <div
        className={cn("fixed bottom-0 left-0 right-0", isMobile ? "p-3" : "p-4")}
        style={{
          backgroundColor: "#141316",
          paddingBottom: isMobile ? "env(safe-area-inset-bottom, 12px)" : "16px",
        }}
      >
        <form onSubmit={handleSubmit} className={cn("mx-auto", isMobile ? "max-w-full" : "max-w-3xl")}>
          <div
            ref={inputContainerRef}
            className={cn(
              "relative w-full rounded-2xl border border-zinc-700 cursor-text",
              isMobile ? "p-3" : "p-3",
              isStreaming && "opacity-80",
            )}
            style={{ backgroundColor: "#27252C" }}
            onClick={handleInputContainerClick}
          >
            <div className={cn(isMobile ? "pb-12" : "pb-9")}>
              <Textarea
                ref={textareaRef}
                placeholder={
                  isStreaming ? "Waiting for response..." : "What are you looking for? a job? a date? Tell me about yourself and I will find you the perfect match!"
                }
                className={cn(
                  "w-full rounded-2xl border-0 bg-transparent text-gray-100 placeholder:text-gray-400 focus:ring-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 resize-none overflow-y-auto font-normal",
                  isMobile
                    ? "min-h-[60px] max-h-[120px] text-sm placeholder:text-sm px-3 py-3 leading-6"
                    : "min-h-[56px] max-h-[160px] text-sm placeholder:text-sm px-3 py-3 leading-6",
                )}
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                onFocus={() => {
                  if (textareaRef.current) {
                    // 移动端需要延迟滚动，等待键盘弹出
                    if (isMobile) {
                      setTimeout(() => {
                        textareaRef.current?.scrollIntoView({ behavior: "smooth", block: "center" })
                      }, 300)
                    } else {
                      textareaRef.current.scrollIntoView({ behavior: "smooth", block: "center" })
                    }
                  }
                }}
                disabled={isStreaming}
                rows={1}
              />
            </div>

            {/* Left side buttons */}
            <div className="absolute bottom-2 left-2 flex items-center space-x-1">
              <Button
                type="button"
                size="icon"
                className={cn(
                  "rounded-full transition-all duration-200 bg-zinc-700 text-gray-400 hover:bg-zinc-600 hover:text-gray-300",
                  isMobile ? "h-8 w-8" : "h-7 w-7"
                )}
                onClick={handleAddSelfie}
                disabled={isStreaming}
              >
                <Camera className={cn(isMobile ? "h-4 w-4" : "h-3.5 w-3.5")} />
                <span className="sr-only">Add selfie</span>
              </Button>
              <span className={cn(
                "text-gray-400 font-normal select-none",
                isMobile ? "text-xs" : "text-xs"
              )}>
                Add selfie - Only reveal after matches!
              </span>
            </div>

            {/* Right side send button */}
            <div className="absolute bottom-2 right-2 flex items-center space-x-2">
              <Button
                type="submit"
                size="icon"
                className={cn(
                  "rounded-full transition-all duration-200",
                  hasTyped && !isStreaming
                    ? "bg-white text-black hover:bg-gray-200"
                    : "bg-zinc-700 text-gray-400 cursor-not-allowed",
                  isMobile ? "h-8 w-8" : "h-7 w-7"
                )}
                disabled={!hasTyped || isStreaming}
                onClick={handleSubmit}
              >
                <ArrowUp className={cn(isMobile ? "h-4 w-4" : "h-3.5 w-3.5")} />
                <span className="sr-only">Send message</span>
              </Button>
            </div>
          </div>
        </form>
      </div>

      {/* 通知设置组件 - 在第一次 AI 回复后触发 */}
      <NotificationSetup
        trigger={shouldTriggerNotificationSetup}
        onSetupComplete={(success) => {
          console.log(`🔔 [NOTIFICATION] Setup completed: ${success}`)
          setShouldTriggerNotificationSetup(false) // 重置触发状态
        }}
      />

      {/* Matches Sidebar */}
      <MatchesSidebar
        isOpen={isSidebarOpen}
        onClose={handleSidebarToggle}
        fid={context?.user?.fid?.toString()}
      />
    </div>
  )
}

export default ChatInterface
