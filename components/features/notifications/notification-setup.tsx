'use client'

import React, { useEffect, useState, forwardRef, useImperativeHandle } from 'react'
import { useFrame } from '@/components/providers/farcaster-provider'
import { handleNotificationSetup } from '@/lib/notifications'

interface NotificationSetupProps {
  onSetupComplete?: (success: boolean) => void
  trigger?: boolean  // 外部触发信号
}

export interface NotificationSetupRef {
  triggerSetup: () => void
  setupState: {
    status: 'idle' | 'setting_up' | 'success' | 'error'
    error?: string
  }
}

/**
 * 简化的通知设置组件
 * 在收到外部触发信号时自动调用 addMiniApp()
 */
export const NotificationSetup = forwardRef<NotificationSetupRef, NotificationSetupProps>(({
  onSetupComplete,
  trigger = false
}, ref) => {
  const { context, isSDKLoaded, actions } = useFrame()
  const [setupState, setSetupState] = useState<{
    status: 'idle' | 'setting_up' | 'success' | 'error'
    error?: string
  }>({ status: 'idle' })
  const [added, setAdded] = useState(false)
  const [notificationDetails, setNotificationDetails] = useState<any>(null)

  // 当收到触发信号时执行设置
  useEffect(() => {
    if (
      trigger &&
      isSDKLoaded &&
      context?.user?.fid &&
      !added &&
      setupState.status === 'idle'
    ) {
      handleSetupNotifications()
    }
  }, [trigger, isSDKLoaded, context?.user?.fid, added, setupState.status])

  // Handle notification details when they become available
  useEffect(() => {
    if (notificationDetails && context?.user?.fid) {
      console.log('📱 [NOTIFICATION SETUP] Processing notification details for FID:', context.user.fid)

      // Just log the success - Neynar handles all storage
      handleNotificationSetup(context.user.fid, notificationDetails)

      setSetupState({ status: 'success' })
      onSetupComplete?.(true)
    }
  }, [notificationDetails, context?.user?.fid, onSetupComplete])

  const handleSetupNotifications = async () => {
    if (!isSDKLoaded || !context?.user?.fid || !actions?.addMiniApp) {
      console.warn('⚠️ [NOTIFICATION SETUP] Prerequisites not met:', {
        sdkLoaded: isSDKLoaded,
        hasFid: !!context?.user?.fid,
        hasAddMiniApp: !!actions?.addMiniApp
      })
      return
    }

    setSetupState({ status: 'setting_up' })
    console.log('📱 [NOTIFICATION SETUP] Starting setup for FID:', context.user.fid)

    try {
      const result = await actions.addMiniApp()

      console.log('📱 [NOTIFICATION SETUP] addMiniApp result:', {
        hasNotificationDetails: !!result.notificationDetails,
        fid: context.user.fid,
        fullResult: result,
        notificationDetails: result.notificationDetails
      })

      if (result.notificationDetails) {
        // Success: mini app was added and notifications are enabled
        setAdded(true)
        setNotificationDetails(result.notificationDetails)

        // Just log the success - Neynar handles all storage
        const success = handleNotificationSetup(context.user.fid, result.notificationDetails)

        setSetupState({ status: 'success' })
        console.log('✅ [NOTIFICATION SETUP] Mini app added with notifications enabled')
        onSetupComplete?.(true)
      } else {
        // User declined or notifications not enabled
        console.warn('⚠️ [NOTIFICATION SETUP] User declined to add mini app or notifications not enabled')
        setSetupState({
          status: 'error',
          error: 'User declined to add mini app or notifications not enabled'
        })
        onSetupComplete?.(false)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      console.error('❌ [NOTIFICATION SETUP] Setup failed:', error)
      setSetupState({ 
        status: 'error', 
        error: errorMessage 
      })
      onSetupComplete?.(false)
    }
  }

  // Manual setup trigger (for UI components)
  const triggerSetup = () => {
    if (setupState.status !== 'setting_up') {
      handleSetupNotifications()
    }
  }

  // Expose setup function for parent components
  useImperativeHandle(ref, () => ({
    triggerSetup,
    setupState
  }))

  // Debug information (only in development)
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 [NOTIFICATION SETUP DEBUG]', {
      isSDKLoaded,
      fid: context?.user?.fid,
      added,
      hasNotificationDetails: !!notificationDetails,
      setupState
    })
  }

  // This is a background component - no UI rendering
  return null
})

NotificationSetup.displayName = 'NotificationSetup'

/**
 * Hook for manual notification setup control
 * Useful for components that need to trigger setup manually
 */
export function useNotificationSetup() {
  const { context, isSDKLoaded, actions } = useFrame()
  const [isSettingUp, setIsSettingUp] = useState(false)
  const [added, setAdded] = useState(false)
  const [notificationDetails, setNotificationDetails] = useState<any>(null)

  const setupNotifications = async (): Promise<boolean> => {
    if (!isSDKLoaded || !context?.user?.fid || isSettingUp || !actions?.addMiniApp) {
      return false
    }

    setIsSettingUp(true)

    try {
      const result = await actions.addMiniApp()

      if (result.notificationDetails && context.user.fid) {
        setAdded(true)
        setNotificationDetails(result.notificationDetails)
        // Just log the success - Neynar handles all storage
        handleNotificationSetup(context.user.fid, result.notificationDetails)
        return true
      }

      return false
    } catch (error) {
      console.error('❌ [NOTIFICATION HOOK] Setup failed:', error)
      return false
    } finally {
      setIsSettingUp(false)
    }
  }

  return {
    setupNotifications,
    isSettingUp,
    isReady: isSDKLoaded && !!context?.user?.fid,
    hasNotifications: !!notificationDetails,
    isAdded: added
  }
}
