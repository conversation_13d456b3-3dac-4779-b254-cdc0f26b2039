'use client'

import { useEffect } from 'react'
import { useNotification } from '@/components/providers/notification-provider'

interface NotificationTriggerProps {
  /**
   * Whether to automatically trigger notifications when backend sends them
   */
  autoTrigger?: boolean
  /**
   * Custom trigger conditions
   */
  triggerConditions?: {
    onNewMessage?: boolean
    onUserActivity?: boolean
    onFocus?: boolean
  }
}

/**
 * Notification Trigger Component
 * 
 * This component handles the logic for triggering chat refreshes
 * when notifications are received from the nmllagent backend.
 * 
 * It's designed to be:
 * 1. Lightweight and focused on notification logic only
 * 2. Easily configurable for different trigger conditions
 * 3. Separate from UI components for better modularity
 */
export function NotificationTrigger({ 
  autoTrigger = true,
  triggerConditions = {
    onNewMessage: true,
    onUserActivity: false,
    onFocus: true
  }
}: NotificationTriggerProps) {
  const { registerRefreshCallback, notificationState } = useNotification()

  // Register for notification callbacks
  useEffect(() => {
    if (!autoTrigger) return

    console.log('🎯 [NOTIFICATION TRIGGER] Registering notification callbacks...')

    const cleanup = registerRefreshCallback(() => {
      console.log('🔔 [NOTIFICATION TRIGGER] Notification callback triggered')
      
      // Here you could add additional logic like:
      // - Show toast notifications
      // - Play sounds
      // - Update badges
      // - Log analytics
    })

    return cleanup
  }, [autoTrigger, registerRefreshCallback])

  // Handle focus-based triggers
  useEffect(() => {
    if (!triggerConditions.onFocus) return

    const handleFocus = () => {
      console.log('👁️ [NOTIFICATION TRIGGER] Window focused, checking for updates...')
      // The actual refresh logic is handled by the chat interface
      // This is just for additional focus-based logic
    }

    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [triggerConditions.onFocus])

  // This component doesn't render anything - it's purely for logic
  return null
}

/**
 * Hook version for components that need notification trigger logic
 */
export function useNotificationTrigger(options: NotificationTriggerProps = {}) {
  const { triggerChatRefresh, notificationState } = useNotification()

  const manualTrigger = () => {
    console.log('🔄 [NOTIFICATION TRIGGER HOOK] Manual trigger activated')
    triggerChatRefresh()
  }

  return {
    manualTrigger,
    lastNotificationTime: notificationState.lastNotificationTime,
    isEnabled: notificationState.isEnabled
  }
}
