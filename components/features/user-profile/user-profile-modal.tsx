"use client";

import React from 'react';
import { ArrowLeft, X, Users, ThumbsUp, ThumbsDown, Minus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { UserProfileProps } from '@/types/user-profile';
import { useSecureSelfie } from '@/hooks/useSecureSelfie';
import { useFrame } from '@/components/providers/farcaster-provider';

// 安全的用户头像组件
function SecureUserAvatar({
  realID,
  platform,
  displayName,
  avatarUrl,
  currentUserFID,
  className = "w-24 h-24"
}: {
  realID: string;
  platform: string;
  displayName: string;
  avatarUrl?: string;
  currentUserFID: string;
  className?: string;
}) {
  const { imageUrl: selfieUrl, isLoading, error } = useSecureSelfie({
    realID,
    platform: platform as 'farcaster' | 'telegram',
    requestorRealID: currentUserFID,
    expires: 3600, // 1小时
    autoRefresh: true
  });

  // 移除有问题的useEffect，避免无限循环

  // 头像优先级：avatarUrl -> selfie -> fallback
  const hasAvatarUrl = avatarUrl && avatarUrl.trim() !== '';
  const hasSelfie = selfieUrl && !error;

  const avatarFallback = displayName
    ? displayName.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
    : 'AU';

  if (hasAvatarUrl) {
    return (
      <img
        src={avatarUrl}
        alt={displayName}
        className={cn(
          className,
          "rounded-full object-cover ring-4 ring-zinc-600/30 shadow-lg"
        )}
        onError={(e) => {
          const target = e.target as HTMLImageElement;
          target.style.display = 'none';
          const fallback = target.nextElementSibling as HTMLElement;
          if (fallback) fallback.style.display = 'flex';
        }}
      />
    );
  }

  if (hasSelfie) {
    return (
      <img
        src={selfieUrl}
        alt={displayName}
        className={cn(
          className,
          "rounded-full object-cover ring-4 ring-zinc-600/30 shadow-lg"
        )}
        onError={(e) => {
          const target = e.target as HTMLImageElement;
          target.style.display = 'none';
          const fallback = target.nextElementSibling as HTMLElement;
          if (fallback) fallback.style.display = 'flex';
        }}
      />
    );
  }

  // Fallback头像
  return (
    <div
      className={cn(
        className,
        "flex items-center justify-center rounded-full",
        "bg-gradient-to-br from-[#C7FF02] via-[#9AE600] to-[#7ACC00]",
        "ring-4 ring-zinc-600/30 shadow-lg",
        "text-2xl font-bold text-black"
      )}
    >
      {avatarFallback}
    </div>
  );
}

// 安全的Selfie显示组件
function SecureSelfieSection({
  realID,
  platform,
  displayName,
  currentUserFID
}: {
  realID: string;
  platform: string;
  displayName: string;
  currentUserFID: string;
}) {
  // 🚨 Debug: Log component render
  console.log('🚨🚨🚨 [SECURE SELFIE SECTION] Component rendered 🚨🚨🚨', {
    realID,
    platform,
    displayName,
    currentUserFID,
    hasRealID: !!realID,
    hasPlatform: !!platform,
    hasCurrentUserFID: !!currentUserFID
  });
  const { imageUrl: selfieUrl, isLoading, error } = useSecureSelfie({
    realID,
    platform: platform as 'farcaster' | 'telegram',
    requestorRealID: currentUserFID,
    expires: 3600, // 1小时
    autoRefresh: true
  });

  if (isLoading) {
    return (
      <div className={cn(
        "p-4 bg-zinc-800/30 backdrop-blur-sm rounded-2xl",
        "border border-zinc-700/30"
      )}>
        <div className="text-center space-y-3">
          <h3 className="text-sm font-semibold text-gray-100">Selfie</h3>
          <div className="flex justify-center">
            <div className="w-47 h-47 rounded-2xl bg-zinc-700 animate-pulse"></div>
          </div>
        </div>
      </div>
    );
  }

  // 🚨 Debug: Show debug info only when no URL or error
  if (!selfieUrl || error) {
    return (
      <div className={cn(
        "p-4 bg-zinc-800/30 backdrop-blur-sm rounded-2xl",
        "border border-zinc-700/30"
      )}>
        <div className="text-center space-y-3">
          <h3 className="text-sm font-semibold text-gray-100">Selfie (Debug)</h3>
          <div className="flex justify-center">
            <div className={cn(
              "w-47 h-47 rounded-2xl bg-red-500/20 flex items-center justify-center",
              "ring-2 ring-red-500/30 shadow-lg"
            )}>
              <span className="text-2xl">❌</span>
            </div>
          </div>
          <div className="text-xs text-gray-400 space-y-1">
            <p>realID: {realID}</p>
            <p>platform: {platform}</p>
            <p>isLoading: {isLoading ? 'true' : 'false'}</p>
            <p>error: {error || 'none'}</p>
            <p>hasUrl: {selfieUrl ? 'true' : 'false'}</p>
            <p>urlLength: {selfieUrl ? selfieUrl.length : 0}</p>
            <p>urlPreview: {selfieUrl ? selfieUrl.substring(0, 50) + '...' : 'none'}</p>
          </div>
          {selfieUrl && (
            <div className="mt-3">
              <img
                src={selfieUrl || ''}
                alt="Debug selfie"
                className="w-16 h-16 rounded-lg object-cover mx-auto border-2 border-green-500"
                onLoad={() => console.log('🚨🚨🚨 IMAGE LOADED SUCCESSFULLY 🚨🚨🚨')}
                onError={(e) => console.log('🚨🚨🚨 IMAGE LOAD FAILED 🚨🚨🚨', e)}
              />
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "p-4 bg-zinc-800/30 backdrop-blur-sm rounded-2xl",
      "border border-zinc-700/30"
    )}>
      <div className="text-center space-y-3">
        <h3 className="text-sm font-semibold text-gray-100">Selfie</h3>
        <div className="flex justify-center">
          <img
            src={selfieUrl || ''}
            alt={`${displayName} selfie`}
            className={cn(
              "w-47 h-47 rounded-2xl object-cover",
              "ring-2 ring-zinc-600/30 shadow-lg"
            )}
            onError={(e) => {
              console.log('❌ [USER PROFILE SELFIE] Image load failed:', selfieUrl);
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
            }}
          />
        </div>
      </div>
    </div>
  );
}

const UserProfileModal: React.FC<UserProfileProps> = ({
  userData = {},
  onReturn,
  onClose,
  className,
  isLoading = false,
  error = null
}) => {
  // 获取当前用户FID和actions
  const { context, actions } = useFrame();
  const currentUserFID = context?.user?.fid?.toString() || '';

  // Prevent background scroll when profile is open
  React.useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  const {
    platform = "Unknown Platform",
    id = "N/A",
    profileId = "N/A",
    displayName = "Anonymous User",
    username = "anonymous",
    avatarUrl,
    description = "No description available",
    score = 0,
    stats
  } = userData;

  // 处理 Farcaster 用户点击跳转
  const handleFarcasterProfileClick = (fid: string) => {
    if (actions?.viewProfile && platform.toLowerCase() === 'farcaster') {
      try {
        const fidNumber = parseInt(fid, 10);
        if (!isNaN(fidNumber)) {
          console.log(`🔗 [USER PROFILE] Opening Farcaster profile for FID: ${fidNumber}`);
          actions.viewProfile({ fid: fidNumber });
        }
      } catch (error) {
        console.error('❌ [USER PROFILE] Failed to open Farcaster profile:', error);
      }
    }
  };

  // 🚨 Debug: Log UserProfile props
  console.log('🚨🚨🚨 [USER PROFILE MODAL] Props extracted 🚨🚨🚨', {
    platform,
    id,
    displayName,
    currentUserFID,
    hasUserData: !!userData,
    userDataKeys: userData ? Object.keys(userData) : null
  });

  // Handle return button click
  const handleReturn = () => {
    if (onReturn) {
      onReturn();
    }
  };

  // Calculate total reviews
  const totalReviews = stats?.review.received 
    ? stats.review.received.positive + stats.review.received.neutral + stats.review.received.negative 
    : 0;

  // Calculate total reviews for stats display

  // Loading state with better animation
  if (isLoading) {
    return (
      <div className={cn(
        "fixed inset-0 z-50 flex items-center justify-center p-4",
        "bg-black/50 backdrop-blur-sm animate-fade-in",
        className
      )}>
        <div className="relative w-[83%] max-w-md mx-auto">
          <div className={cn(
            "relative bg-zinc-900/90 backdrop-blur-xl border border-zinc-700/50",
            "rounded-3xl shadow-2xl shadow-black/25 p-8"
          )}>
            {/* Header skeleton */}
            <div className="flex items-center justify-between mb-6">
              <div className="w-8 h-8 bg-zinc-700 rounded-full animate-pulse" />
              <div className="flex flex-col items-center space-y-1">
                <div className="w-16 h-4 bg-zinc-700 rounded animate-pulse" />
                <div className="w-12 h-3 bg-zinc-700 rounded animate-pulse" />
              </div>
              <div className="w-8 h-8 bg-zinc-700 rounded-full animate-pulse" />
            </div>

            {/* Avatar and info skeleton */}
            <div className="flex flex-col items-center space-y-4">
              <div className="relative">
                <div className="w-24 h-24 bg-zinc-700 rounded-full animate-pulse" />
                <div className="absolute -bottom-3 -right-3 w-12 h-12 bg-gradient-to-br from-[#C7FF02] via-[#9AE600] to-[#7ACC00] rounded-full animate-pulse" />
              </div>
              <div className="text-center space-y-2">
                <div className="w-32 h-5 bg-zinc-700 rounded animate-pulse mx-auto" />
                <div className="w-24 h-4 bg-zinc-700 rounded animate-pulse mx-auto" />
                <div className="w-40 h-3 bg-zinc-700 rounded animate-pulse mx-auto" />
              </div>
            </div>

            {/* Loading message */}
            <div className="mt-6 text-center">
              <p className="text-sm text-gray-400">Loading profile...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn(
        "fixed inset-0 z-50 flex items-center justify-center p-4",
        "bg-black/50 backdrop-blur-sm",
        className
      )}>
        <div className="relative w-[83%] max-w-md mx-auto">
          <div className={cn(
            "relative bg-zinc-900/90 backdrop-blur-xl border border-zinc-700/50",
            "rounded-3xl shadow-2xl shadow-black/25 p-6"
          )}>
            <div className="flex items-center justify-between mb-4">
              <button
                onClick={handleReturn}
                className={cn(
                  "flex items-center justify-center w-10 h-10",
                  "bg-zinc-800/50 hover:bg-zinc-700/50 backdrop-blur-sm",
                  "rounded-full border border-zinc-600/50",
                  "transition-all duration-200 hover:scale-105"
                )}
              >
                <ArrowLeft className="w-5 h-5 text-gray-100" />
              </button>
              
              <button
                onClick={onClose}
                className={cn(
                  "flex items-center justify-center w-10 h-10",
                  "bg-zinc-800/50 hover:bg-zinc-700/50 backdrop-blur-sm",
                  "rounded-full border border-zinc-600/50",
                  "transition-all duration-200 hover:scale-105"
                )}
              >
                <X className="w-5 h-5 text-gray-100" />
              </button>
            </div>
            
            <div className="text-center space-y-4">
              <div className="w-12 h-12 mx-auto bg-red-500/20 rounded-full flex items-center justify-center">
                <X className="w-6 h-6 text-red-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-100">Error Loading Profile</h3>
              <p className="text-sm text-gray-400">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "fixed inset-0 z-50 flex items-center justify-center p-4",
      "bg-black/50 backdrop-blur-sm",
      className
    )}>
      {/* Main Profile Container - Scrollable */}
      <div className="relative w-[83%] max-w-md mx-auto max-h-[90vh] overflow-y-auto">
        <div className={cn(
          "relative bg-zinc-900/90 backdrop-blur-xl border border-zinc-700/50",
          "rounded-3xl shadow-2xl shadow-black/25",
          "my-4"
        )}>
          {/* Gradient Background Overlay - adapted to match farcaster theme */}
          <div className="absolute inset-0 bg-gradient-to-br from-zinc-800/20 to-transparent rounded-3xl" />
          
          {/* Header with Navigation */}
          <div className="relative flex items-center justify-between p-6 pb-4">
            <button
              onClick={handleReturn}
              className={cn(
                "flex items-center justify-center w-10 h-10",
                "bg-zinc-800/50 hover:bg-zinc-700/50 backdrop-blur-sm",
                "rounded-full border border-zinc-600/50",
                "transition-all duration-200 hover:scale-105"
              )}
            >
              <ArrowLeft className="w-5 h-5 text-gray-100" />
            </button>
            
            <div className="flex flex-col items-center">
              <h1 className="text-lg font-semibold text-gray-100">Profile</h1>
              <p className="text-xs text-gray-400">{platform}</p>
            </div>
            
            <button
              onClick={onClose}
              className={cn(
                "flex items-center justify-center w-10 h-10",
                "bg-zinc-800/50 hover:bg-zinc-700/50 backdrop-blur-sm",
                "rounded-full border border-zinc-600/50",
                "transition-all duration-200 hover:scale-105"
              )}
            >
              <X className="w-5 h-5 text-gray-100" />
            </button>
          </div>

          {/* Profile Content */}
          <div className="relative px-6 pb-6 space-y-6">
            {/* Avatar Section */}
            <div className="flex flex-col items-center space-y-4">
              <div className="relative">
                <SecureUserAvatar
                  realID={id}
                  platform={platform}
                  displayName={displayName}
                  avatarUrl={avatarUrl}
                  currentUserFID={currentUserFID}
                  className="w-24 h-24"
                />
                
                {/* Premium Score Display - keep original green gradient */}
                <div className={cn(
                  "absolute -bottom-3 -right-3",
                  "flex items-center justify-center w-12 h-12",
                  "bg-gradient-to-br from-[#C7FF02] via-[#9AE600] to-[#7ACC00]",
                  "rounded-full shadow-xl border-2 border-zinc-600/30",
                  "backdrop-blur-sm"
                )}>
                  <div className="text-center">
                    <div className="text-xs font-bold text-black leading-none">{score}</div>
                    <div className="text-[8px] text-black/70 leading-none">SCORE</div>
                  </div>
                </div>
              </div>

              {/* Basic Info */}
              <div className="text-center space-y-1">
                <h2 className="text-xl font-bold text-gray-100">{displayName}</h2>
                <p className="text-gray-300">@{username}</p>
                <div className="flex items-center justify-center space-x-4 text-xs text-gray-500">
                  <span>ID: {id}</span>
                  <span>•</span>
                  <span>
                    Profile: {platform.toLowerCase() === 'farcaster' ? (
                      <button
                        onClick={() => handleFarcasterProfileClick(profileId)}
                        className="text-blue-400 hover:text-blue-300 hover:underline cursor-pointer bg-transparent border-none p-0 font-inherit ml-1"
                        style={{
                          background: 'none',
                          border: 'none',
                          padding: 0,
                          font: 'inherit',
                          cursor: 'pointer',
                          outline: 'inherit'
                        }}
                      >
                        {profileId}
                      </button>
                    ) : profileId}
                  </span>
                </div>
              </div>
            </div>

            {/* Description */}
            <div className={cn(
              "p-4 bg-zinc-800/30 backdrop-blur-sm rounded-2xl",
              "border border-zinc-700/30"
            )}>
              <p className="text-sm text-gray-200 leading-relaxed text-center">
                {description}
              </p>
            </div>

            {/* Stats Section */}
            {stats && (
              <div className="space-y-4">
                {/* Reviews */}
                <div className={cn(
                  "p-4 bg-zinc-800/30 backdrop-blur-sm rounded-2xl",
                  "border border-zinc-700/30"
                )}>
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-semibold text-gray-100">Reviews</h3>
                    <span className="text-xs text-gray-400">{totalReviews} total</span>
                  </div>

                  <div className="grid grid-cols-3 gap-3">
                    <div className="text-center">
                      <div className="flex items-center justify-center w-8 h-8 mx-auto mb-1 bg-green-500/20 rounded-full">
                        <ThumbsUp className="w-4 h-4 text-green-400" />
                      </div>
                      <div className="text-lg font-bold text-gray-100">{stats.review.received.positive}</div>
                      <div className="text-xs text-gray-400">Positive</div>
                    </div>

                    <div className="text-center">
                      <div className="flex items-center justify-center w-8 h-8 mx-auto mb-1 bg-yellow-500/20 rounded-full">
                        <Minus className="w-4 h-4 text-yellow-400" />
                      </div>
                      <div className="text-lg font-bold text-gray-100">{stats.review.received.neutral}</div>
                      <div className="text-xs text-gray-400">Neutral</div>
                    </div>

                    <div className="text-center">
                      <div className="flex items-center justify-center w-8 h-8 mx-auto mb-1 bg-red-500/20 rounded-full">
                        <ThumbsDown className="w-4 h-4 text-red-400" />
                      </div>
                      <div className="text-lg font-bold text-gray-100">{stats.review.received.negative}</div>
                      <div className="text-xs text-gray-400">Negative</div>
                    </div>
                  </div>
                </div>

                {/* Vouches */}
                <div className={cn(
                  "p-4 bg-zinc-800/30 backdrop-blur-sm rounded-2xl",
                  "border border-zinc-700/30"
                )}>
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-semibold text-gray-100">Vouches</h3>
                    <Users className="w-4 h-4 text-gray-400" />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-xs text-gray-400 mb-1">Received</div>
                      <div className="text-sm font-bold text-gray-100">{stats.vouch.received.count} vouches</div>
                    </div>

                    <div>
                      <div className="text-xs text-gray-400 mb-1">Given</div>
                      <div className="text-sm font-bold text-gray-100">{stats.vouch.given.count} vouches</div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Selfie Section */}
            <SecureSelfieSection
              realID={id}
              platform={platform}
              displayName={displayName}
              currentUserFID={currentUserFID}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfileModal;
