'use client';

import { useState, useEffect } from 'react';
import { X, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { UserProfileModal } from '@/components/features/user-profile';
import { useUserProfile } from '@/hooks/use-user-profile';
import { matchDataToFetchOptions } from '@/lib/user-data-service';
import { useSecureSelfie } from '@/hooks/useSecureSelfie';
import { farcasterAuth } from '@/lib/farcaster-auth';
import { useFrame } from '@/components/providers/farcaster-provider';

// 获取平台图标
const getPlatformIcon = (platform: string) => {
  switch (platform.toLowerCase()) {
    case 'farcaster':
      return '🟣';
    case 'telegram':
      return '💙';
    default:
      return '👤';
  }
};

interface MatchData {
  id: string;
  username: string;
  matchedUsername: string;
  postMessage: string;
  timestamp: number;
  platform: string;
  realID: string;
}

interface MatchesSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  fid?: string;
}

// 匹配用户头像组件
function MatchUserAvatar({ match, currentUserFID }: { match: MatchData; currentUserFID: string }) {
  const { imageUrl, isLoading, error } = useSecureSelfie({
    realID: match.realID,
    platform: match.platform as 'farcaster' | 'telegram',
    requestorRealID: currentUserFID,
    expires: 1800, // 30分钟过期（侧边栏刷新频繁）
    autoRefresh: true
  });

  // 如果有selfie URL，显示selfie
  if (imageUrl && !error) {
    return (
      <img
        src={imageUrl}
        alt={`${match.matchedUsername} selfie`}
        className="w-10 h-10 rounded-full object-cover flex-shrink-0"
        onError={(e) => {
          // 如果selfie加载失败，隐藏图片，显示fallback
          const target = e.target as HTMLImageElement;
          target.style.display = 'none';
          const fallback = target.nextElementSibling as HTMLElement;
          if (fallback) fallback.style.display = 'flex';
        }}
      />
    );
  }

  // Fallback：显示平台图标
  return (
    <div className="w-10 h-10 bg-zinc-700 rounded-full flex items-center justify-center flex-shrink-0">
      <span className="text-lg">{getPlatformIcon(match.platform)}</span>
    </div>
  );
}

export default function MatchesSidebar({ isOpen, onClose, fid }: MatchesSidebarProps) {
  const [matches, setMatches] = useState<MatchData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取当前用户FID
  const { context } = useFrame();
  const currentUserFID = context?.user?.fid?.toString() || fid || '';

  // User profile modal state
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [selectedMatch, setSelectedMatch] = useState<MatchData | null>(null);

  // User profile hook
  const {
    profileData,
    isLoading: isProfileLoading,
    error: profileError,
    fetchUserProfile,
    reset: resetProfile
  } = useUserProfile();

  // Fetch matches when sidebar opens
  useEffect(() => {
    if (isOpen && fid) {
      fetchMatches();
    }
  }, [isOpen, fid]);

  const fetchMatches = async () => {
    if (!fid) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // 🔒 SECURITY: FID now extracted from JWT token on backend
      const response = await farcasterAuth.authenticatedFetch('/api/nmllagent/matches');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch matches: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        setMatches(data.matches || []);
      } else {
        setError(data.error || 'Failed to load matches');
      }
    } catch (err) {
      console.error('Error fetching matches:', err);
      setError(err instanceof Error ? err.message : 'Failed to load matches');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMatchClick = async (match: MatchData) => {
    console.log('🔍 [SIDEBAR] Match clicked:', match);

    // Set selected match and open profile modal
    setSelectedMatch(match);
    setIsProfileOpen(true);

    // Fetch user profile data
    const fetchOptions = matchDataToFetchOptions(match);
    await fetchUserProfile(fetchOptions);
  };

  // Handle profile modal close
  const handleProfileClose = () => {
    setIsProfileOpen(false);
    setSelectedMatch(null);
    resetProfile();
  };

  // Handle profile return (back to sidebar)
  const handleProfileReturn = () => {
    setIsProfileOpen(false);
    // Keep selected match and profile data in case user wants to go back
  };

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  return (
    <>
      {/* Backdrop */}
      <div
        className={cn(
          "fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-300",
          isOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        )}
        onClick={onClose}
      />

      {/* Sidebar */}
      <div
        className={cn(
          "fixed top-0 right-0 h-full w-2/3 bg-zinc-900 border-l border-zinc-700 z-50 transition-transform duration-300 ease-in-out",
          isOpen ? "translate-x-0" : "translate-x-full"
        )}
        style={{ backgroundColor: "#141316" }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-zinc-700">
          <h2 className="text-lg font-medium text-gray-100">My Matches</h2>
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full hover:bg-zinc-800 h-8 w-8"
            onClick={onClose}
          >
            <X className="h-4 w-4 text-gray-400" />
            <span className="sr-only">Close</span>
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center p-8">
              <div className="text-gray-400 text-sm">Loading matches...</div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center p-8">
              <div className="text-red-400 text-sm text-center">
                {error}
              </div>
            </div>
          ) : matches.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <User className="h-12 w-12 text-gray-600 mb-4" />
              <div className="text-gray-400 text-sm mb-2">No matches yet</div>
              <div className="text-gray-500 text-xs">
                Start chatting to find your perfect matches!
              </div>
            </div>
          ) : (
            <div className="p-2">
              {matches.map((match) => (
                <div
                  key={match.id}
                  className={cn(
                    "p-3 rounded-lg mb-2 cursor-pointer transition-all duration-200",
                    "hover:bg-zinc-800 active:bg-zinc-700 active:scale-[0.98]"
                  )}
                  onClick={() => handleMatchClick(match)}
                >
                  <div className="flex items-start space-x-3">
                    <MatchUserAvatar match={match} currentUserFID={currentUserFID} />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-100 truncate">
                          @{match.matchedUsername}
                        </span>
                        <span className="text-xs text-gray-500 flex-shrink-0 ml-2">
                          {formatTimestamp(match.timestamp)}
                        </span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-400 capitalize">
                          {match.platform}
                        </span>
                        <span className="text-xs text-gray-500">•</span>
                        <span className="text-xs text-gray-500 font-mono">
                          ID: {match.realID}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* User Profile Modal */}
      {isProfileOpen && (
        <UserProfileModal
          userData={profileData || undefined}
          isLoading={isProfileLoading}
          error={profileError}
          onReturn={handleProfileReturn}
          onClose={handleProfileClose}
        />
      )}
    </>
  );
}
