'use client';

import { useRef, useEffect, useState, useCallback } from 'react';
import { X, Camera, RotateCcw, AlertCircle, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CameraInterfaceProps {
  onClose: () => void;
  onPhotoCapture?: (photoBlob: Blob) => void;
}

export default function CameraInterface({ onClose, onPhotoCapture }: CameraInterfaceProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [facingMode, setFacingMode] = useState<'user' | 'environment'>('environment');
  const [isCapturing, setIsCapturing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasUserCamera, setHasUserCamera] = useState(false);
  const [hasEnvironmentCamera, setHasEnvironmentCamera] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [captureSuccess, setCaptureSuccess] = useState(false);

  const checkAvailableCameras = useCallback(async () => {
    console.log('🎥 [DEBUG] checkAvailableCameras started');
    try {
      // First request permission
      console.log('🎥 [DEBUG] Requesting temporary camera permission...');
      const tempStream = await navigator.mediaDevices.getUserMedia({ video: true });
      // Stop the temporary stream immediately
      tempStream.getTracks().forEach(track => track.stop());
      console.log('🎥 [DEBUG] Temporary stream stopped');

      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      console.log('🎥 [DEBUG] Available video devices:', videoDevices);

      if (videoDevices.length === 0) {
        setError('No camera devices found');
        return;
      }

      setHasUserCamera(videoDevices.some(device =>
        device.label.toLowerCase().includes('front') ||
        device.label.toLowerCase().includes('user')
      ));
      setHasEnvironmentCamera(videoDevices.some(device =>
        device.label.toLowerCase().includes('back') ||
        device.label.toLowerCase().includes('environment') ||
        (!device.label.toLowerCase().includes('front') && !device.label.toLowerCase().includes('user'))
      ));

      if (videoDevices.length === 1) {
        setHasUserCamera(true);
        setHasEnvironmentCamera(true);
      }
    } catch (err) {
      console.error('Error checking available cameras:', err);
      if (err instanceof Error) {
        if (err.name === 'NotAllowedError') {
          setError('Camera permission denied. Please allow camera access and refresh the page.');
        } else if (err.name === 'NotFoundError') {
          setError('No camera device found');
        } else {
          setError('Unable to access camera. Please check device settings.');
        }
      }
    }
  }, []);

  const startCamera = useCallback(async () => {
    // Stop any previous stream
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
    }

    setError(null);
    setIsLoading(true);
    setCaptureSuccess(false);

    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      setError('Your browser does not support camera functionality. Please use a modern browser.');
      setIsLoading(false);
      return;
    }

    const advancedConstraints = {
      video: {
        facingMode: facingMode,
        width: { ideal: 1280, max: 1920 },
        height: { ideal: 720, max: 1080 }
      }
    };

    const basicConstraints = { video: true };

    let newStream: MediaStream | null = null;
    try {
      console.log('Attempting to start camera with advanced constraints:', advancedConstraints);
      newStream = await navigator.mediaDevices.getUserMedia(advancedConstraints);
      console.log('Successfully started camera with advanced constraints.');
    } catch (err) {
      console.warn('Advanced constraints failed, falling back to basic constraints.', err);
      if (err instanceof Error && err.name !== 'OverconstrainedError' && err.name !== 'NotFoundError') {
        console.error('Error accessing camera:', err);
        setIsLoading(false);
        if (err.name === 'NotAllowedError') {
          setError('Camera permission denied. Please click the camera icon in the address bar to allow access, then refresh the page.');
        } else if (err.name === 'NotReadableError') {
          setError('Camera is being used by another application. Please close other camera apps and try again.');
        } else {
          setError(`Camera startup failed: ${err.message}`);
        }
        return;
      }

      try {
        console.log('Attempting to start camera with basic constraints.');
        newStream = await navigator.mediaDevices.getUserMedia(basicConstraints);
        console.log('Successfully started camera with basic constraints.');
      } catch (finalErr) {
        console.error('Failed to start camera with basic constraints:', finalErr);
        setIsLoading(false);
        if (finalErr instanceof Error) {
          if (finalErr.name === 'NotFoundError') {
            setError('No camera device found. Please ensure your device has a camera and it is not being used by other applications.');
          } else {
            setError(`Camera startup failed: ${finalErr.message}`);
          }
        }
        return;
      }
    }

    setStream(newStream);

    // After successfully getting stream, check available cameras
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      const videoDevices = devices.filter(device => device.kind === 'videoinput');
      console.log('🎥 [DEBUG] Available video devices after stream:', videoDevices);

      setHasUserCamera(videoDevices.some(device =>
        device.label.toLowerCase().includes('front') ||
        device.label.toLowerCase().includes('user')
      ));
      setHasEnvironmentCamera(videoDevices.some(device =>
        device.label.toLowerCase().includes('back') ||
        device.label.toLowerCase().includes('environment') ||
        (!device.label.toLowerCase().includes('front') && !device.label.toLowerCase().includes('user'))
      ));

      if (videoDevices.length === 1) {
        setHasUserCamera(true);
        setHasEnvironmentCamera(true);
      }
    } catch (deviceErr) {
      console.warn('🎥 [DEBUG] Failed to enumerate devices:', deviceErr);
      // Default to showing both camera options
      setHasUserCamera(true);
      setHasEnvironmentCamera(true);
    }

    // Loading state will be handled in the useEffect below
  }, [facingMode]); // Remove stream from dependencies to prevent infinite loop

  const switchCamera = useCallback(() => {
    const newFacingMode = facingMode === 'user' ? 'environment' : 'user';
    setFacingMode(newFacingMode);
  }, [facingMode]);

  const capturePhoto = useCallback(async () => {
    if (!videoRef.current || !canvasRef.current || !stream || videoRef.current.readyState < 2) {
      setError('Camera not ready. Please try again.');
      return;
    }

    setIsCapturing(true);
    setCaptureSuccess(false);

    try {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      if (!context) {
        setError('Unable to create canvas context');
        setIsCapturing(false);
        return;
      }

      if (video.videoWidth === 0 || video.videoHeight === 0) {
        setError('Video not loaded yet. Please try again.');
        setIsCapturing(false);
        return;
      }

      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      if (facingMode === 'user') {
        context.scale(-1, 1);
        context.drawImage(video, -canvas.width, 0, canvas.width, canvas.height);
      } else {
        context.drawImage(video, 0, 0, canvas.width, canvas.height);
      }

      // Convert to WebP format
      canvas.toBlob((blob) => {
        if (blob) {
          console.log('Photo captured successfully:', blob);
          setCaptureSuccess(true);
          
          // Call the callback if provided for future nmllagent integration
          if (onPhotoCapture) {
            onPhotoCapture(blob);
          }
          
          // Hide success message after 2 seconds
          setTimeout(() => {
            setCaptureSuccess(false);
          }, 2000);
        } else {
          setError('Failed to capture photo');
        }
      }, 'image/webp', 0.9); // WebP format with 90% quality

      // Flash effect
      const flashOverlay = document.createElement('div');
      flashOverlay.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background: white; z-index: 9999; pointer-events: none; opacity: 0.8;
      `;
      document.body.appendChild(flashOverlay);
      
      setTimeout(() => {
        if (document.body.contains(flashOverlay)) {
          document.body.removeChild(flashOverlay);
        }
      }, 150);

    } catch (err) {
      console.error('Error capturing photo:', err);
      setError('Photo capture failed. Please try again.');
    } finally {
      setTimeout(() => setIsCapturing(false), 300);
    }
  }, [facingMode, stream, onPhotoCapture]);

  // 1. Start camera when component mounts or facingMode changes
  useEffect(() => {
    console.log('🎥 [DEBUG] Starting camera with facingMode:', facingMode);
    startCamera();
  }, [facingMode]); // Simplified: only depend on facingMode

  // 3. Assign stream to video element
  useEffect(() => {
    if (stream && videoRef.current) {
      console.log("Assigning stream to video element.");
      videoRef.current.srcObject = stream;

      videoRef.current.onloadedmetadata = () => {
        console.log("Video metadata loaded. Dimensions:", videoRef.current?.videoWidth, videoRef.current?.videoHeight);
        setIsLoading(false);
        setError(null);
        videoRef.current?.play().catch(e => console.error("Video play failed:", e));
      };

      videoRef.current.onerror = (e) => {
        console.error("Video element error:", e);
        setError('Video loading failed');
        setIsLoading(false);
      };
    }

    // Cleanup function: stop all video tracks when component unmounts or stream changes
    return () => {
      if (stream) {
        console.log("Stopping all media tracks.");
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, [stream]);

  const handleClose = () => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
    }
    onClose();
  };

  const handleRetry = () => {
    setError(null);
    setIsLoading(true);
    startCamera();
  };

  if (error) {
    return (
      <div className="fixed inset-0 flex items-center justify-center p-4" style={{ backgroundColor: "#141316" }}>
        <div className="rounded-2xl p-8 max-w-md w-full text-center space-y-6" style={{ backgroundColor: "#27252C" }}>
          <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto">
            <AlertCircle className="w-8 h-8 text-red-400" />
          </div>
          <div className="space-y-2">
            <h3 className="text-lg font-medium text-gray-100">Camera Error</h3>
            <p className="text-gray-300 text-sm leading-relaxed font-normal">{error}</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleRetry}
              className="flex-1 bg-white text-black py-3 rounded-xl hover:bg-gray-200 transition-colors duration-200 font-medium"
            >
              Retry
            </button>
            <button
              onClick={handleClose}
              className="flex-1 bg-zinc-700 text-gray-100 py-3 rounded-xl hover:bg-zinc-600 transition-colors duration-200 font-medium"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0" style={{ backgroundColor: "#141316" }}>
      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center z-30" style={{ backgroundColor: "#141316" }}>
          <div className="text-center space-y-4">
            <div className="w-12 h-12 border-4 border-white/30 border-t-white rounded-full animate-spin mx-auto"></div>
            <p className="text-white text-sm font-normal">Starting camera...</p>
          </div>
        </div>
      )}

      {/* Success Message - Farcaster miniapp style */}
      {captureSuccess && (
        <div className="absolute top-20 left-1/2 transform -translate-x-1/2 z-40 max-w-[85%]">
          <div
            className="px-4 py-3 rounded-2xl text-gray-100 text-sm font-normal flex items-center space-x-2 shadow-lg"
            style={{ backgroundColor: "#27252C" }}
          >
            <CheckCircle className="w-4 h-4 text-green-400" />
            <span>Photo captured successfully!</span>
          </div>
        </div>
      )}

      {/* Video Preview */}
      <video
        ref={videoRef}
        autoPlay
        playsInline
        muted
        className={cn(
          "absolute inset-0 w-full h-full object-cover",
          facingMode === 'user' ? 'scale-x-[-1]' : '',
          isLoading ? 'opacity-0' : 'opacity-100',
          'transition-opacity duration-500'
        )}
      />

      {/* Hidden Canvas for Photo Capture */}
      <canvas ref={canvasRef} className="hidden" />

      {/* Top Controls */}
      <div className="absolute top-0 left-0 right-0 bg-gradient-to-b from-black/50 to-transparent p-4 z-10">
        <div className="flex justify-between items-center">
          <button
            onClick={handleClose}
            className="w-10 h-10 bg-black/30 backdrop-blur-md rounded-full flex items-center justify-center hover:bg-black/40 transition-colors duration-200"
          >
            <X className="w-5 h-5 text-white" />
          </button>

          <div className="text-white text-sm font-normal bg-black/30 backdrop-blur-md px-3 py-1.5 rounded-full">
            {facingMode === 'user' ? 'Front Camera' : 'Back Camera'}
          </div>
        </div>
      </div>

      {/* Bottom Controls */}
      {!isLoading && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/50 to-transparent p-6 z-10">
          <div className="flex items-center justify-center space-x-8">
            {/* Switch Camera Button */}
            {(hasUserCamera && hasEnvironmentCamera) && (
              <button
                onClick={switchCamera}
                className="w-12 h-12 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center hover:bg-white/30 transition-all duration-200 hover:scale-105"
              >
                <RotateCcw className="w-6 h-6 text-white" />
              </button>
            )}

            {/* Capture Button */}
            <button
              onClick={capturePhoto}
              disabled={isCapturing}
              className={cn(
                "relative w-24 h-24 bg-white/20 backdrop-blur-md rounded-full flex items-center justify-center hover:bg-white/30 transition-all duration-200 hover:scale-105 active:scale-95",
                isCapturing ? 'opacity-50' : ''
              )}
            >
              <Camera className="w-12 h-12 text-white" />
              {isCapturing && (
                <div className="absolute inset-0 border-4 border-blue-500 rounded-full animate-pulse"></div>
              )}
            </button>

            {/* Placeholder for symmetry */}
            <div className="w-12 h-12"></div>
          </div>

          {/* Instructions */}
          <div className="text-center mt-4">
            <p className="text-white/80 text-sm font-normal">
              Tap to capture a selfie for your profile
            </p>
          </div>
        </div>
      )}

      {/* Capture Animation Overlay */}
      {isCapturing && (
        <div className="absolute inset-0 bg-white/20 z-20 pointer-events-none"></div>
      )}
    </div>
  );
}
