'use client'

import React from 'react'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; reset: () => void }>
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback
      return (
        <FallbackComponent 
          error={this.state.error!} 
          reset={() => this.setState({ hasError: false, error: undefined })}
        />
      )
    }

    return this.props.children
  }
}

function DefaultErrorFallback({ error, reset }: { error: Error; reset: () => void }) {
  return (
    <div className="min-h-screen bg-[#141316] text-white flex items-center justify-center p-4">
      <div className="max-w-md w-full text-center space-y-4">
        <h1 className="text-2xl font-bold">Something went wrong</h1>
        <p className="text-gray-400">
          The application encountered an error. This might be due to:
        </p>
        <ul className="text-left text-sm text-gray-400 space-y-1">
          <li>• Backend service not configured</li>
          <li>• Network connectivity issues</li>
          <li>• Farcaster SDK initialization problems</li>
        </ul>
        <details className="text-left">
          <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-300">
            Error details
          </summary>
          <pre className="mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded overflow-auto">
            {error.message}
          </pre>
        </details>
        <button
          onClick={reset}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors"
        >
          Try again
        </button>
        <p className="text-xs text-gray-500">
          If the problem persists, check the browser console for more details.
        </p>
      </div>
    </div>
  )
}
