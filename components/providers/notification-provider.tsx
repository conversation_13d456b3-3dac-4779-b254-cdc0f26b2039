'use client'

import React, { createContext, useContext, useCallback, useState } from 'react'
import { triggerChatRefresh } from '@/lib/frame-notifications'

interface NotificationContextValue {
  /**
   * Trigger a chat refresh manually
   */
  triggerChatRefresh: () => void
  /**
   * Register a callback to be called when notifications are received
   */
  registerRefreshCallback: (callback: () => void) => () => void
  /**
   * Get the current notification state
   */
  notificationState: {
    lastNotificationTime: number | null
    isEnabled: boolean
  }
  /**
   * Enable/disable notifications
   */
  setNotificationsEnabled: (enabled: boolean) => void
}

const NotificationContext = createContext<NotificationContextValue | undefined>(undefined)

export function useNotification() {
  const context = useContext(NotificationContext)
  if (context === undefined) {
    throw new Error('useNotification must be used within a NotificationProvider')
  }
  return context
}

interface NotificationProviderProps {
  children: React.ReactNode
}

/**
 * Notification Provider Component
 * 
 * Provides centralized notification management across the app.
 * This allows different components to:
 * 1. Register for notification callbacks
 * 2. Trigger manual refreshes
 * 3. Manage notification state
 */
export function NotificationProvider({ children }: NotificationProviderProps) {
  const [notificationState, setNotificationState] = useState({
    lastNotificationTime: null as number | null,
    isEnabled: true
  })
  
  // Store registered callbacks
  const [refreshCallbacks, setRefreshCallbacks] = useState<Set<() => void>>(new Set())

  // Manual trigger function
  const handleTriggerChatRefresh = useCallback(() => {
    console.log('🔄 [NOTIFICATION PROVIDER] Manual chat refresh triggered')
    triggerChatRefresh()
    
    // Update last notification time
    setNotificationState(prev => ({
      ...prev,
      lastNotificationTime: Date.now()
    }))
    
    // Call all registered callbacks
    refreshCallbacks.forEach(callback => {
      try {
        callback()
      } catch (error) {
        console.error('❌ [NOTIFICATION PROVIDER] Error in refresh callback:', error)
      }
    })
  }, [refreshCallbacks])

  // Register callback function
  const registerRefreshCallback = useCallback((callback: () => void) => {
    console.log('📝 [NOTIFICATION PROVIDER] Registering refresh callback')
    
    setRefreshCallbacks(prev => {
      const newSet = new Set(prev)
      newSet.add(callback)
      return newSet
    })

    // Return cleanup function
    return () => {
      console.log('🧹 [NOTIFICATION PROVIDER] Unregistering refresh callback')
      setRefreshCallbacks(prev => {
        const newSet = new Set(prev)
        newSet.delete(callback)
        return newSet
      })
    }
  }, [])

  // Enable/disable notifications
  const setNotificationsEnabled = useCallback((enabled: boolean) => {
    console.log(`🔔 [NOTIFICATION PROVIDER] Notifications ${enabled ? 'enabled' : 'disabled'}`)
    setNotificationState(prev => ({
      ...prev,
      isEnabled: enabled
    }))
  }, [])

  const contextValue: NotificationContextValue = {
    triggerChatRefresh: handleTriggerChatRefresh,
    registerRefreshCallback,
    notificationState,
    setNotificationsEnabled
  }

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  )
}
