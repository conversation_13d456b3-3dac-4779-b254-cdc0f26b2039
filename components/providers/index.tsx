'use client'

import { FrameProvider } from '@/components/providers/farcaster-provider'
import { WalletProvider } from '@/components/providers/wallet-provider'
import { NotificationProvider } from '@/components/providers/notification-provider'
import { ErrorBoundary } from '@/components/layout/error-boundary'

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ErrorBoundary>
      <WalletProvider>
        <FrameProvider>
          <NotificationProvider>
            {children}
          </NotificationProvider>
        </FrameProvider>
      </WalletProvider>
    </ErrorBoundary>
  )
}
