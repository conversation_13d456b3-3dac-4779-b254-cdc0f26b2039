import type { Context } from '@farcaster/miniapp-sdk'
import sdk from '@farcaster/miniapp-sdk'
import { useQuery } from '@tanstack/react-query'
import { type ReactNode, createContext, useContext } from 'react'

interface FrameContextValue {
  context: Context.MiniAppContext | undefined
  isLoading: boolean
  isSDKLoaded: boolean
  isEthProviderAvailable: boolean
  actions: typeof sdk.actions | undefined
}

const FrameProviderContext = createContext<FrameContextValue | undefined>(
  undefined,
)

export function useFrame() {
  const context = useContext(FrameProviderContext)
  if (context === undefined) {
    throw new Error('useFrame must be used within a FrameProvider')
  }
  return context
}

interface FrameProviderProps {
  children: ReactNode
}

export function FrameProvider({ children }: FrameProviderProps) {
  const farcasterContextQuery = useQuery({
    queryKey: ['farcaster-context'],
    queryFn: async () => {
      try {
        const context = await sdk.context

        // Debug: Log the full context data
        console.log('🔍 [FARCASTER SDK] Full context:', context)
        console.log('🔍 [FARCASTER SDK] User data:', context?.user)
        console.log('🔍 [FARCASTER SDK] User FID:', context?.user?.fid)
        console.log('🔍 [FARCASTER SDK] User username:', context?.user?.username)
        console.log('🔍 [FARCASTER SDK] User displayName:', context?.user?.displayName)

        try {
          await sdk.actions.ready()
          return { context, isReady: true, error: null }
        } catch (err) {
          console.error('SDK initialization error:', err)
          return { context, isReady: false, error: err }
        }
      } catch (err) {
        console.error('SDK context error:', err)
        return { context: null, isReady: false, error: err }
      }
    },
    retry: false, // Don't retry on failure
    refetchOnWindowFocus: false, // Don't refetch when window gains focus
  })

  const isReady = farcasterContextQuery.data?.isReady ?? false

  return (
    <FrameProviderContext.Provider
      value={{
        context: farcasterContextQuery.data?.context ?? undefined,
        actions: sdk.actions,
        isLoading: farcasterContextQuery.isPending,
        isSDKLoaded: isReady && Boolean(farcasterContextQuery.data?.context),
        isEthProviderAvailable: Boolean(sdk.wallet.ethProvider),
      }}
    >
      {children}
    </FrameProviderContext.Provider>
  )
}
