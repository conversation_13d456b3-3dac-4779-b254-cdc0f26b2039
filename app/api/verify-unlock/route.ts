import { NextRequest, NextResponse } from 'next/server'

interface VerifyUnlockRequest {
  password: string
  type: 'static_password' | 'invitation_code'
}

interface VerifyUnlockResponse {
  success: boolean
  message?: string
}

// Static password verification
const verifyStaticPassword = (password: string): boolean => {
  const staticPassword = process.env.LOCK_SCREEN_PASSWORD || 'nomorelonely2024'
  return password === staticPassword
}

// Future: Invitation code verification with nmllagent backend
// Note: This would need to be updated to use Farcaster Quick Auth when implemented
const verifyInvitationCode = async (code: string): Promise<boolean> => {
  try {
    const nmllagentUrl = process.env.NEXT_PUBLIC_NMLLAGENT_URL

    if (!nmllagentUrl) {
      console.warn('NMLLAgent configuration missing, falling back to static password')
      return false
    }

    // TODO: Implement Quick Auth token verification for invitation codes
    // For now, return false to use static password fallback
    console.warn('Invitation code verification not yet implemented with Quick Auth')
    return false

    /* Future implementation with Quick Auth:
    const response = await fetch(`${nmllagentUrl}/api/verify-invitation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${quickAuthToken}`,
      },
      body: JSON.stringify({
        invitationCode: code,
        platform: 'farcaster'
      }),
    })
    */
  } catch (error) {
    console.error('Invitation code verification failed:', error)
    return false
  }
}

export async function POST(request: NextRequest) {
  try {
    const body: VerifyUnlockRequest = await request.json()
    const { password, type } = body

    if (!password || typeof password !== 'string') {
      return NextResponse.json(
        { success: false, message: 'Password is required' },
        { status: 400 }
      )
    }

    let isValid = false
    let message = ''

    // Try different verification methods
    if (type === 'static_password') {
      isValid = verifyStaticPassword(password)
      message = isValid ? 'Access granted' : 'Invalid password'
    } else {
      // Try invitation code first, then fallback to static password
      isValid = await verifyInvitationCode(password)
      
      if (!isValid) {
        // Fallback to static password
        isValid = verifyStaticPassword(password)
        message = isValid 
          ? 'Access granted with static password' 
          : 'Invalid invitation code or password'
      } else {
        message = 'Access granted with invitation code'
      }
    }

    const response: VerifyUnlockResponse = {
      success: isValid,
      message
    }

    return NextResponse.json(response, { 
      status: isValid ? 200 : 401 
    })

  } catch (error) {
    console.error('Unlock verification error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}
