import { NextRequest, NextResponse } from 'next/server'

const NMLLAGENT_URL = process.env.NEXT_PUBLIC_NMLLAGENT_URL || ''

// Check if nmllagent backend is configured
// In development, localhost is allowed; in production, avoid localhost
const isBackendConfigured = NMLLAGENT_URL && NMLLAGENT_URL !== '' &&
  (process.env.NODE_ENV === 'development' || !NMLLAGENT_URL.includes('localhost'))

// Validate request to prevent abuse
function validateRequest(body: any): { valid: boolean; error?: string } {
  if (!body.text || typeof body.text !== 'string') {
    return { valid: false, error: 'Invalid or missing text field' }
  }
  if (!body.fid || typeof body.fid !== 'string') {
    return { valid: false, error: 'Invalid or missing fid field' }
  }
  // Remove message length limit - LLM interactions may need longer messages
  // Remove rate limiting - multiple users share same frontend IP
  // Remove FID format validation - may be too restrictive
  return { valid: true }
}

export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`\n🚀 [${timestamp}] === MESSAGE PROXY START ===`)

  try {
    console.log('📥 [STEP 1] Parsing request body...')
    const body = await request.json()
    console.log('✅ [STEP 1] Request body parsed:', JSON.stringify(body, null, 2))

    // Validate request format (basic validation only)
    const validation = validateRequest(body)
    if (!validation.valid) {
      console.log('❌ [STEP 1.5] Request validation failed:', validation.error)
      return NextResponse.json({
        error: validation.error,
        timestamp
      }, { status: 400 })
    }

    // Check if backend is configured
    if (!isBackendConfigured) {
      console.log('⚠️ [STEP 2] Backend not configured, returning mock response')
      return NextResponse.json({
        response: "Hello! I'm a demo response since the nmllagent backend is not configured yet. Please set up your NEXT_PUBLIC_NMLLAGENT_URL environment variable to connect to a real backend.",
        success: true,
        timestamp,
        demo: true
      })
    }

    // Note: Authentication is now handled via Quick Auth tokens passed through from frontend

    console.log('🔗 [STEP 3] Preparing to proxy to nmllagent...')
    console.log('🎯 [STEP 3] Target URL:', NMLLAGENT_URL)
    console.log('🎯 [STEP 3] Full endpoint:', `${NMLLAGENT_URL}/api/farcaster/message`)

    // Extract Quick Auth token from request headers
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('❌ [STEP 3] Missing or invalid authorization header')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    console.log('🔐 [STEP 3] Using Quick Auth token for backend authentication')

    console.log('📡 [STEP 4] Sending authenticated request to nmllagent...')
    const startTime = Date.now()

    const response = await fetch(`${NMLLAGENT_URL}/api/farcaster/message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader, // Forward the Quick Auth token
        'User-Agent': 'farcaster-miniapp/1.0',
      },
      body: JSON.stringify(body),
    })

    const endTime = Date.now()
    console.log(`⏱️ [STEP 4] Request completed in ${endTime - startTime}ms`)
    console.log(`📊 [STEP 4] Response status: ${response.status} ${response.statusText}`)
    console.log(`📊 [STEP 4] Response headers:`, Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      console.log('❌ [STEP 5] Response not OK, reading error...')
      const errorText = await response.text()
      console.error('❌ [STEP 5] nmllagent error details:', {
        status: response.status,
        statusText: response.statusText,
        errorBody: errorText
      })

      // Handle authentication errors specifically
      if (response.status === 401) {
        return NextResponse.json(
          {
            error: 'Authentication failed',
            details: 'Invalid Quick Auth token',
            timestamp
          },
          { status: 401 }
        )
      }

      return NextResponse.json(
        {
          error: `nmllagent error: ${response.status}`,
          details: errorText,
          timestamp
        },
        { status: response.status }
      )
    }

    console.log('✅ [STEP 4] Response OK, parsing JSON...')
    const data = await response.json()
    console.log('✅ [STEP 4] nmllagent response data:', JSON.stringify(data, null, 2))

    console.log('🎉 [STEP 5] Returning success response to client')
    console.log(`🏁 [${timestamp}] === MESSAGE PROXY END ===\n`)

    return NextResponse.json(data)
  } catch (error) {
    const errorDetails = error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
      cause: error.cause
    } : {
      name: 'Unknown',
      message: String(error),
      stack: undefined,
      cause: undefined
    }

    console.error(`💥 [ERROR] Proxy error at ${timestamp}:`, errorDetails)
    console.log(`🏁 [${timestamp}] === MESSAGE PROXY END (ERROR) ===\n`)

    return NextResponse.json(
      {
        error: 'Failed to connect to nmllagent',
        details: errorDetails.message,
        timestamp
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
