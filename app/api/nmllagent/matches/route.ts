import { NextRequest, NextResponse } from 'next/server'

// Environment variables
const NMLLAGENT_URL = process.env.NEXT_PUBLIC_NMLLAGENT_URL

// Check if backend is properly configured
const isBackendConfigured = NMLLAGENT_URL &&
  NMLLAGENT_URL !== '' &&
  (process.env.NODE_ENV === 'development' || !NMLLAGENT_URL.includes('localhost'))

interface MatchData {
  id: string
  username: string
  matchedUsername: string
  postMessage: string
  timestamp: number
}

interface MatchesResponse {
  success: boolean
  matches: MatchData[]
  error?: string
  timestamp: string
}

export async function GET(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`\n🔍 [${timestamp}] === MATCHES API START ===`)

  try {
    // 🔒 [STEP 1] Quick Auth token validation
    console.log('🔒 [STEP 1] Checking Quick Auth token...')
    const authHeader = request.headers.get('authorization')

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('❌ [STEP 1] Missing or invalid authorization header')
      return NextResponse.json(
        { error: 'Authentication required', timestamp },
        { status: 401 }
      )
    }

    console.log('✅ [STEP 1] Quick Auth token present, will be verified by backend')

    // 🔒 [STEP 2] SECURITY: FID will be extracted from JWT token on backend
    console.log('🔒 [STEP 2] FID will be extracted from JWT token on backend for security')

    // Note: Authorization will be handled by the backend using Quick Auth token

    // Check if backend is configured
    if (!isBackendConfigured) {
      console.log('⚠️ [STEP 4] Backend not configured, returning empty matches')
      return NextResponse.json({
        success: true,
        matches: [],
        timestamp,
        demo: true
      })
    }

    console.log('🔗 [STEP 3] Preparing to proxy to nmllagent...')
    console.log('🎯 [STEP 3] Target URL:', NMLLAGENT_URL)
    // 🔒 SECURITY: No FID parameter needed - extracted from JWT token on backend
    const fullUrl = `${NMLLAGENT_URL}/api/farcaster/matches`
    console.log('🎯 [STEP 3] Full endpoint:', fullUrl)

    console.log('🔐 [STEP 3] Using Quick Auth token for backend authentication')

    console.log('📡 [STEP 4] Sending authenticated request to nmllagent...')
    const startTime = Date.now()

    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader, // Forward the Quick Auth token
      },
    })

    const endTime = Date.now()
    console.log(`📡 [STEP 6] Request completed in ${endTime - startTime}ms`)
    console.log('📡 [STEP 6] Response status:', response.status, response.statusText)

    if (!response.ok) {
      const errorText = await response.text()
      console.log('❌ [STEP 6] nmllagent returned error:', response.status, errorText)

      return NextResponse.json({
        error: `Backend error: ${response.status} ${response.statusText}`,
        details: errorText,
        timestamp
      }, { status: response.status })
    }

    console.log('✅ [STEP 6] Response OK, parsing JSON...')
    const data = await response.json()
    console.log('✅ [STEP 6] nmllagent matches data:', {
      success: data.success,
      matchesLength: data.matches?.length || 0,
      sampleData: data.matches?.slice(0, 2) // Show first 2 matches for debugging
    })

    console.log('🎉 [STEP 7] Returning success response to client')
    console.log(`🏁 [${timestamp}] === MATCHES API END ===\n`)

    return NextResponse.json(data)
  } catch (error) {
    const errorDetails = error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
      cause: error.cause
    } : {
      name: 'Unknown',
      message: String(error),
      stack: undefined,
      cause: undefined
    }

    console.log('💥 [ERROR] Exception in matches proxy:', errorDetails)
    console.log(`🏁 [${timestamp}] === MATCHES API END (ERROR) ===\n`)

    return NextResponse.json({
      error: 'Internal server error',
      details: errorDetails.message,
      timestamp
    }, { status: 500 })
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}
