import { NextRequest, NextResponse } from 'next/server'

const NMLLAGENT_URL = process.env.NEXT_PUBLIC_NMLLAGENT_URL || ''

// Check if nmllagent backend is configured
// In development, localhost is allowed; in production, avoid localhost
const isBackendConfigured = NMLLAGENT_URL && NMLLAGENT_URL !== '' &&
  (process.env.NODE_ENV === 'development' || !NMLLAGENT_URL.includes('localhost'))

export async function GET(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`\n🧪 [${timestamp}] === CONNECTION TEST START ===`)
  
  const testResults = {
    timestamp,
    nmllagentUrl: NMLLAGENT_URL,
    backendConfigured: isBackendConfigured,
    tests: [] as any[]
  }

  // Check if backend is configured
  if (!isBackendConfigured) {
    console.log('⚠️ Backend not configured, returning configuration info')
    testResults.tests.push({
      name: 'Backend Configuration',
      configured: false,
      message: 'NEXT_PUBLIC_NMLLAGENT_URL is not configured or points to localhost',
      success: false
    })

    return NextResponse.json(testResults)
  }

  try {
    // Test 1: Basic connectivity to nmllagent
    console.log('🔍 [TEST 1] Testing basic connectivity to nmllagent...')
    const test1Start = Date.now()
    
    try {
      const response = await fetch(`${NMLLAGENT_URL}/helloworld`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      })
      
      const test1End = Date.now()
      const responseText = await response.text()
      
      testResults.tests.push({
        name: 'Basic Connectivity',
        endpoint: `${NMLLAGENT_URL}/helloworld`,
        status: response.status,
        statusText: response.statusText,
        responseTime: test1End - test1Start,
        responseBody: responseText,
        success: response.ok
      })
      
      console.log(`✅ [TEST 1] Basic connectivity: ${response.status} in ${test1End - test1Start}ms`)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      testResults.tests.push({
        name: 'Basic Connectivity',
        endpoint: `${NMLLAGENT_URL}/helloworld`,
        error: errorMessage,
        success: false
      })
      console.log(`❌ [TEST 1] Basic connectivity failed:`, errorMessage)
    }
    
    // Test 2: Farcaster history endpoint
    console.log('🔍 [TEST 2] Testing farcaster history endpoint...')
    const test2Start = Date.now()
    
    try {
      // 🔒 SECURITY: FID now extracted from JWT token on backend (this test will fail without auth)
      const response = await fetch(`${NMLLAGENT_URL}/api/farcaster/history`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      })
      
      const test2End = Date.now()
      const responseText = await response.text()
      
      testResults.tests.push({
        name: 'Farcaster History Endpoint',
        endpoint: `${NMLLAGENT_URL}/api/farcaster/history`,
        status: response.status,
        statusText: response.statusText,
        responseTime: test2End - test2Start,
        responseBody: responseText.substring(0, 200) + (responseText.length > 200 ? '...' : ''),
        success: response.ok
      })
      
      console.log(`✅ [TEST 2] History endpoint: ${response.status} in ${test2End - test2Start}ms`)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      testResults.tests.push({
        name: 'Farcaster History Endpoint',
        endpoint: `${NMLLAGENT_URL}/api/farcaster/history`,
        error: errorMessage,
        success: false
      })
      console.log(`❌ [TEST 2] History endpoint failed:`, errorMessage)
    }
    
    // Test 3: Environment info
    console.log('🔍 [TEST 3] Gathering environment info...')
    testResults.tests.push({
      name: 'Environment Info',
      nodeEnv: process.env.NODE_ENV,
      nextPublicUrl: process.env.NEXT_PUBLIC_URL,
      nmllagentUrl: process.env.NEXT_PUBLIC_NMLLAGENT_URL,
      success: true
    })
    
    console.log(`🏁 [${timestamp}] === CONNECTION TEST END ===\n`)
    
    return NextResponse.json(testResults)
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    console.error(`💥 [ERROR] Connection test error at ${timestamp}:`, error)

    return NextResponse.json({
      ...testResults,
      error: errorMessage
    }, { status: 500 })
  }
}
