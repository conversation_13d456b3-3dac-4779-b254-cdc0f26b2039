import { NextRequest, NextResponse } from 'next/server'

/**
 * Proxy endpoint for selfie upload to nmllagent backend
 * Handles multipart/form-data and forwards to nmllagent
 */
export async function POST(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`\n🔄 [${timestamp}] === SELFIE UPLOAD PROXY START ===`)

  try {
    console.log('🔄 [SELFIE PROXY] Received selfie upload request')
    console.log('🔄 [SELFIE PROXY] Request URL:', request.url)
    console.log('🔄 [SELFIE PROXY] Request method:', request.method)
    console.log('🔄 [SELFIE PROXY] Request headers:', Object.fromEntries(request.headers.entries()))

    // Get nmllagent backend URL from environment
    const nmllagentUrl = process.env.NEXT_PUBLIC_NMLLAGENT_URL
    console.log('🔄 [SELFIE PROXY] Backend URL from env:', nmllagentUrl)

    if (!nmllagentUrl) {
      console.error('❌ [SELFIE PROXY] NEXT_PUBLIC_NMLLAGENT_URL not configured')
      return NextResponse.json(
        { error: 'Backend configuration error' },
        { status: 500 }
      )
    }

    // Extract Quick Auth token from request headers
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.error('❌ [SELFIE PROXY] Missing or invalid authorization header')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get the form data from the request (back to FormData approach)
    console.log('📤 [SELFIE PROXY] Parsing form data...')
    const formData = await request.formData()
    console.log('📤 [SELFIE PROXY] Form data parsed successfully')

    // Log the form data for debugging
    console.log('📤 [SELFIE PROXY] Form data fields:')
    for (const [key, value] of formData.entries()) {
      if (value instanceof File) {
        console.log(`  ${key}: File(${value.name}, ${value.size} bytes, ${value.type})`)
      } else {
        console.log(`  ${key}: ${value}`)
      }
    }
    console.log('📤 [SELFIE PROXY] Form data logging complete')

    // Forward the request to nmllagent backend
    const backendUrl = `${nmllagentUrl}/api/selfie/upload`
    console.log(`🔗 [SELFIE PROXY] Forwarding to: ${backendUrl}`)
    console.log(`🔗 [SELFIE PROXY] Request headers:`, {
      'Authorization': authHeader ? `${authHeader.substring(0, 20)}...` : 'MISSING'
    })

    console.log('📡 [SELFIE PROXY] Sending request to backend...')
    const fetchStartTime = Date.now()

    const response = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        'Authorization': authHeader, // Forward the Quick Auth token
        // Don't set Content-Type - let fetch handle it for FormData
      },
      body: formData, // Send FormData directly
    })

    const fetchEndTime = Date.now()
    console.log(`📊 [SELFIE PROXY] Backend response: ${response.status} ${response.statusText}`)
    console.log(`📊 [SELFIE PROXY] Response time: ${fetchEndTime - fetchStartTime}ms`)
    console.log(`📊 [SELFIE PROXY] Response headers:`, Object.fromEntries(response.headers.entries()))

    // Get response data
    console.log('📥 [SELFIE PROXY] Parsing response JSON...')
    let responseData
    try {
      responseData = await response.json()
      console.log('📥 [SELFIE PROXY] Response data:', JSON.stringify(responseData, null, 2))
    } catch (jsonError) {
      console.error('❌ [SELFIE PROXY] Failed to parse response JSON:', jsonError)
      const responseText = await response.text()
      console.error('❌ [SELFIE PROXY] Raw response text:', responseText)
      return NextResponse.json(
        {
          error: 'Invalid response from backend',
          details: `Status: ${response.status}, Text: ${responseText}`
        },
        { status: 502 }
      )
    }

    if (!response.ok) {
      console.error('❌ [SELFIE PROXY] Backend error:', responseData)
      console.log(`🏁 [${timestamp}] === SELFIE UPLOAD PROXY END (ERROR) ===\n`)
      return NextResponse.json(
        responseData,
        { status: response.status }
      )
    }

    console.log('✅ [SELFIE PROXY] Selfie upload successful')
    console.log(`🏁 [${timestamp}] === SELFIE UPLOAD PROXY END (SUCCESS) ===\n`)
    return NextResponse.json(responseData)

  } catch (error) {
    console.error('❌ [SELFIE PROXY] Proxy error:', error)
    console.error('❌ [SELFIE PROXY] Error stack:', error instanceof Error ? error.stack : 'No stack')
    console.log(`🏁 [${timestamp}] === SELFIE UPLOAD PROXY END (EXCEPTION) ===\n`)
    return NextResponse.json(
      {
        error: 'Proxy server error',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp
      },
      { status: 500 }
    )
  }
}

// Handle OPTIONS for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
