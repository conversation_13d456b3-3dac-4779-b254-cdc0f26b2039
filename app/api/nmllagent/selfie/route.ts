/**
 * 🔒 Secure Selfie API Proxy
 * 
 * Proxies selfie requests to nmllagent backend using query parameters
 * Uses Quick Auth for authentication
 */

import { NextRequest, NextResponse } from 'next/server'

const NMLLAGENT_URL = process.env.NEXT_PUBLIC_NMLLAGENT_URL

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 [SELFIE GET] Request received')

    // Extract query parameters
    const { searchParams } = new URL(request.url)
    const realID = searchParams.get('realID')
    const platform = searchParams.get('platform')
    const requestorRealID = searchParams.get('requestorRealID')
    const expires = searchParams.get('expires')

    console.log('🔍 [SELFIE GET] Parameters:', { realID, platform, requestorRealID, expires })

    // Validate required parameters
    if (!realID || !platform) {
      console.error('❌ [SELFIE GET] Missing required parameters')
      return NextResponse.json(
        { error: 'Missing required parameters: realID and platform' },
        { status: 400 }
      )
    }

    // Extract Quick Auth token from request headers
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.error('❌ [SELFIE GET] Missing or invalid authorization header')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // 构建后端API URL
    const backendUrl = `${NMLLAGENT_URL}/api/selfie`
    const queryParams = new URLSearchParams()
    
    queryParams.append('realID', realID)
    queryParams.append('platform', platform)
    
    if (requestorRealID) {
      queryParams.append('requestorRealID', requestorRealID)
    }
    if (expires) {
      queryParams.append('expires', expires)
    }

    const fullBackendUrl = `${backendUrl}?${queryParams.toString()}`

    console.log(`🔗 [SELFIE GET] Forwarding to: ${fullBackendUrl}`)

    // 调用后端API获取签名URL
    const response = await fetch(fullBackendUrl, {
      method: 'GET',
      headers: {
        'Authorization': authHeader, // Forward the Quick Auth token
        'Content-Type': 'application/json',
      },
    })

    console.log(`📊 [SELFIE GET] Backend response: ${response.status} ${response.statusText}`)

    const responseData = await response.json()

    if (!response.ok) {
      console.error('❌ [SELFIE GET] Backend error:', responseData)
      return NextResponse.json(responseData, { status: response.status })
    }

    console.log('✅ [SELFIE GET] Success')
    return NextResponse.json(responseData)

  } catch (error) {
    console.error('❌ [SELFIE GET] Proxy error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: { 
          message: 'Internal server error', 
          code: 500 
        } 
      },
      { status: 500 }
    )
  }
}
