import { NextRequest, NextResponse } from 'next/server'

const NMLLAGENT_URL = process.env.NEXT_PUBLIC_NMLLAGENT_URL || ''

// Check if nmllagent backend is configured
// In development, localhost is allowed; in production, avoid localhost
const isBackendConfigured = NMLLAGENT_URL && NMLLAGENT_URL !== '' &&
  (process.env.NODE_ENV === 'development' || !NMLLAGENT_URL.includes('localhost'))

export async function GET(request: NextRequest) {
  const timestamp = new Date().toISOString()
  console.log(`\n📜 [${timestamp}] === HISTORY PROXY START ===`)

  try {
    // 🔒 [STEP 1] SECURITY: No FID parameter needed - extracted from JWT token on backend
    console.log('🔒 [STEP 1] FID will be extracted from JWT token on backend for security')

    // Check if backend is configured
    if (!isBackendConfigured) {
      console.log('⚠️ [STEP 2] Backend not configured, returning empty history')
      return NextResponse.json({
        history: [],
        success: true,
        timestamp,
        demo: true
      })
    }

    // Note: Authentication is now handled via Quick Auth tokens passed through from frontend

    console.log('🔗 [STEP 3] Preparing to proxy to nmllagent...')
    console.log('🎯 [STEP 3] Target URL:', NMLLAGENT_URL)
    // 🔒 SECURITY: No FID parameter needed - extracted from JWT token on backend
    const fullUrl = `${NMLLAGENT_URL}/api/farcaster/history`
    console.log('🎯 [STEP 3] Full endpoint:', fullUrl)

    // Extract Quick Auth token from request headers
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('❌ [STEP 3] Missing or invalid authorization header')
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    console.log('🔐 [STEP 3] Using Quick Auth token for backend authentication')

    console.log('📡 [STEP 4] Sending authenticated request to nmllagent...')
    const startTime = Date.now()

    const response = await fetch(fullUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader, // Forward the Quick Auth token
        'User-Agent': 'farcaster-miniapp/1.0',
      },
    })

    const endTime = Date.now()
    console.log(`⏱️ [STEP 3] Request completed in ${endTime - startTime}ms`)
    console.log(`📊 [STEP 3] Response status: ${response.status} ${response.statusText}`)
    console.log(`📊 [STEP 3] Response headers:`, Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      console.log('❌ [STEP 4] Response not OK, reading error...')
      const errorText = await response.text()
      console.error('❌ [STEP 4] nmllagent error details:', {
        status: response.status,
        statusText: response.statusText,
        errorBody: errorText
      })
      return NextResponse.json(
        {
          error: `nmllagent error: ${response.status}`,
          details: errorText,
          timestamp
        },
        { status: response.status }
      )
    }

    console.log('✅ [STEP 4] Response OK, parsing JSON...')
    const data = await response.json()
    console.log('✅ [STEP 4] nmllagent history data:', {
      success: data.success,
      historyLength: data.history?.length || 0,
      sampleData: data.history?.slice(0, 2) // Show first 2 messages for debugging
    })

    console.log('🎉 [STEP 5] Returning success response to client')
    console.log(`🏁 [${timestamp}] === HISTORY PROXY END ===\n`)

    return NextResponse.json(data)
  } catch (error) {
    const errorDetails = error instanceof Error ? {
      name: error.name,
      message: error.message,
      stack: error.stack,
      cause: error.cause
    } : {
      name: 'Unknown',
      message: String(error),
      stack: undefined,
      cause: undefined
    }

    console.error(`💥 [ERROR] History proxy error at ${timestamp}:`, errorDetails)
    console.log(`🏁 [${timestamp}] === HISTORY PROXY END (ERROR) ===\n`)

    return NextResponse.json(
      {
        error: 'Failed to connect to nmllagent',
        details: errorDetails.message,
        timestamp
      },
      { status: 500 }
    )
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
