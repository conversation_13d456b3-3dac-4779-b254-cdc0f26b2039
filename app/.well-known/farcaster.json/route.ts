import { NextResponse } from "next/server";
import { generateFarcasterManifest, APP_CONFIG } from "../../../lib/app-config";
import { mnemonicToAccount } from "viem/accounts";

export async function GET() {
  let farcasterConfig = generateFarcasterManifest();

  // Add account association for domain verification
  // Method 1: Use pre-generated account association from environment variable
  if (process.env.ACCOUNT_ASSOCIATION) {
    try {
      const accountAssociation = JSON.parse(process.env.ACCOUNT_ASSOCIATION);
      farcasterConfig.accountAssociation = accountAssociation;
      console.log('✅ [MANIFEST] Added account association from environment variable');
    } catch (error) {
      console.error('❌ [MANIFEST] Failed to parse ACCOUNT_ASSOCIATION:', error);
    }
  }
  // Method 2: Generate account association from seed phrase (fallback)
  else if (process.env.SEED_PHRASE && process.env.FARCASTER_FID) {
    try {
      // Generate account from seed phrase
      const account = mnemonicToAccount(process.env.SEED_PHRASE);
      const custodyAddress = account.address;

      // Extract domain from APP_CONFIG.baseUrl
      const domain = new URL(APP_CONFIG.baseUrl).hostname;

      const header = {
        fid: parseInt(process.env.FARCASTER_FID),
        type: 'custody',
        key: custodyAddress,
      };
      const encodedHeader = Buffer.from(JSON.stringify(header), 'utf-8').toString('base64');

      const payload = {
        domain
      };
      const encodedPayload = Buffer.from(JSON.stringify(payload), 'utf-8').toString('base64url');

      const signature = await account.signMessage({
        message: `${encodedHeader}.${encodedPayload}`
      });
      const encodedSignature = Buffer.from(signature, 'utf-8').toString('base64url');

      // Add account association to the config
      farcasterConfig.accountAssociation = {
        header: encodedHeader,
        payload: encodedPayload,
        signature: encodedSignature
      };

      console.log('✅ [MANIFEST] Generated account association from seed phrase');
    } catch (error) {
      console.error('❌ [MANIFEST] Failed to generate account association:', error);
    }
  } else {
    console.warn('⚠️ [MANIFEST] Missing ACCOUNT_ASSOCIATION or (SEED_PHRASE + FARCASTER_FID) - domain verification will fail');
    console.warn('   Please use Farcaster Manifest Tool to generate Account Association:');
    console.warn('   https://warpcast.com/~/developers/frames');
  }

  console.log('📋 [MANIFEST] Generated Farcaster manifest:', {
    webhookUrl: farcasterConfig.frame.webhookUrl,
    version: farcasterConfig.frame.version,
    hasAccountAssociation: !!farcasterConfig.accountAssociation
  });

  return NextResponse.json(farcasterConfig);
}
