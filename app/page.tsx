import App from '@/components/features/app'
import { generateFrameEmbedMetadata, APP_CONFIG } from '@/lib/app-config'
import type { Metadata } from 'next'

const frame = generateFrameEmbedMetadata()

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: APP_CONFIG.name,
    openGraph: {
      title: APP_CONFIG.name,
      description: APP_CONFIG.description,
    },
    other: {
      'fc:frame': JSON.stringify(frame),
    },
  }
}

export default function Home() {
  return <App />
}
