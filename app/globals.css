@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 20 14 22;
    --foreground: 210 40% 98%;
    --card: 20 14 22;
    --card-foreground: 210 40% 98%;
    --popover: 20 14 22;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --radius: 0.5rem;

    /* Original farcaster template variables */
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }

  /* 移动端优化 */
  html {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
    /* 防止移动端缩放和滚动问题 */
    touch-action: manipulation;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
  }

  /* 安全区域支持 */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* 防止移动端缩放 */
  @media (max-width: 768px) {
    input,
    textarea,
    select {
      font-size: 16px !important;
    }
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
}

@layer components {
  .animate-fade-in {
    animation: fade-in 0.3s ease-in-out;
  }

  /* 移动端触摸优化 */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* 移动端滚动优化 */
  .scroll-smooth {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* 移动端聊天容器优化 */
  .mobile-chat-container {
    /* 确保在移动端有正确的滚动行为 */
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
    /* 防止橡皮筋效果 */
    overscroll-behavior-x: none;
    /* 优化触摸滚动 */
    touch-action: pan-y;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 移动端特定样式 */
@media (max-width: 768px) {
  /* 防止横屏时内容被虚拟键盘遮挡 */
  @media (orientation: landscape) and (max-height: 500px) {
    .mobile-landscape-adjust {
      padding-bottom: 20px !important;
    }
  }
}

/* Original farcaster template styles for backward compatibility */
.farcaster-original-body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}
