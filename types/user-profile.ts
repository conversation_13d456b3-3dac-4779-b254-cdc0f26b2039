/**
 * TypeScript type definitions for User Profile functionality
 * Includes types for Ethos Network API, UserProfile component, and match data
 */

// ===== ETHOS NETWORK API TYPES =====

export interface EthosReviewStats {
  received: {
    negative: number;
    neutral: number;
    positive: number;
  };
}

export interface EthosVouchStats {
  given: {
    amountWeiTotal: number;
    count: number;
  };
  received: {
    amountWeiTotal: number;
    count: number;
  };
}

export interface EthosUserStats {
  review: EthosReviewStats;
  vouch: EthosVouchStats;
}

export interface EthosUserData {
  id: number;
  profileId: number;
  displayName: string;
  username: string;
  avatarUrl: string;
  description: string;
  score: number;
  status: string;
  userkeys: string[];
  xpTotal: number;
  xpStreakDays: number;
  stats: EthosUserStats;
}

export interface EthosApiError {
  code: string;
  message: string;
  issues?: any[];
}

export interface EthosApiResponse {
  success: boolean;
  data?: EthosUserData[];
  error?: EthosApiError;
}

// ===== USER PROFILE COMPONENT TYPES =====

export interface UserProfileStats {
  review: {
    received: {
      negative: number;
      neutral: number;
      positive: number;
    };
  };
  vouch: {
    given: {
      amountWeiTotal: number;
      count: number;
    };
    received: {
      amountWeiTotal: number;
      count: number;
    };
  };
}

export interface UserProfileData {
  platform?: string;
  id?: string;
  profileId?: string;
  displayName?: string;
  username?: string;
  avatarUrl?: string;
  description?: string;
  score?: number;
  stats?: UserProfileStats;
  selfie?: string;
}

export interface UserProfileProps {
  userData?: UserProfileData;
  onReturn?: () => void;
  onClose?: () => void;
  className?: string;
  isLoading?: boolean;
  error?: string | null;
}

// ===== MATCH DATA TYPES =====

export interface MatchData {
  id: string;
  username: string;
  matchedUsername: string;
  postMessage: string;
  timestamp: number;
  platform: string;
  realID: string;
}

export interface MatchesSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  fid?: string;
}

// ===== USER DATA FETCHING TYPES =====

export interface FallbackUserData {
  platform: string;
  realID: string;
  username: string;
  avatarUrl?: string;
  timestamp?: number;
}

export interface UserDataFetchResult {
  success: boolean;
  data?: UserProfileData;
  error?: string;
  source: 'ethos' | 'fallback' | 'error';
}

export interface UserDataFetchOptions {
  platform: string;
  realID: string;
  username: string;
  fallbackData?: FallbackUserData;
}

// ===== PLATFORM CONFIGURATION TYPES =====

export interface PlatformConfig {
  apiEndpoint: string;
  idFieldName: string;
  requestBodyKey: string;
  displayName: string;
  icon?: string;
}

export type SupportedPlatform = 'telegram' | 'farcaster';

// ===== API RESPONSE TYPES =====

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp?: string;
  source?: string;
}

export interface MatchesApiResponse extends ApiResponse {
  matches: MatchData[];
  demo?: boolean;
}

// ===== COMPONENT STATE TYPES =====

export interface UserProfileModalState {
  isOpen: boolean;
  userData: UserProfileData | null;
  isLoading: boolean;
  error: string | null;
  selectedMatch: MatchData | null;
}

export interface UserProfileModalActions {
  openProfile: (match: MatchData) => void;
  closeProfile: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setUserData: (data: UserProfileData | null) => void;
}

// ===== UTILITY TYPES =====

export type PlatformDetectionResult = {
  platform: SupportedPlatform | 'unknown';
  isSupported: boolean;
  normalizedPlatform: string;
};

export type DataTransformResult = {
  transformed: UserProfileData;
  source: 'ethos' | 'fallback';
  warnings?: string[];
};

// ===== ERROR HANDLING TYPES =====

export interface UserProfileError {
  type: 'network' | 'api' | 'validation' | 'unknown';
  message: string;
  details?: any;
  platform?: string;
  userId?: string;
}

export type ErrorHandler = (error: UserProfileError) => void;

// ===== LOADING STATE TYPES =====

export interface LoadingState {
  isLoading: boolean;
  stage: 'idle' | 'fetching-ethos' | 'processing' | 'fallback' | 'complete' | 'error';
  message?: string;
}

// ===== HOOK TYPES =====

export interface UseUserProfileReturn {
  profileData: UserProfileData | null;
  isLoading: boolean;
  error: string | null;
  fetchUserProfile: (options: UserDataFetchOptions) => Promise<void>;
  clearError: () => void;
  reset: () => void;
}

export interface UseUserProfileModalReturn extends UserProfileModalState {
  actions: UserProfileModalActions;
}

// ===== CONSTANTS TYPES =====

export interface EthosNetworkConfig {
  baseUrl: string;
  clientHeader: string;
  timeout: number;
  retryAttempts: number;
}

// ===== VALIDATION TYPES =====

export type ValidationResult = {
  isValid: boolean;
  errors: string[];
  warnings: string[];
};

export type UserDataValidator = (data: any) => ValidationResult;
export type PlatformValidator = (platform: string) => ValidationResult;
export type UserIdValidator = (userId: string, platform: string) => ValidationResult;
